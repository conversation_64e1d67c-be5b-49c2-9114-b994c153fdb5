# 后端详细设计 (LLD) - 迭代三 & 四 (修正最终版)

## 项目结构与总体设计

本次迭代将在迭代二的基础上，完成抄送功能的两大核心闭环：**1) 按身份筛选工单列表** 和 **2) 向抄送人发送飞书通知**。设计完全遵循【**独立状态字段模型**】。

1.  **列表筛选:**
    *   **核心策略:** **筛选逻辑的构建在每个列表专属的DAO方法中独立实现**。前端的抽象角色 `filter_by_role` 数组，由Service层原样传递给DAO层，DAO层负责将其解析为具体的SQL `WHERE`条件。
    *   **返回模型:** API统一返回 `is_cc` 和 `approval_status_for_me` 两个整型字段，供前端使用。

2.  **消息通知:**
    *   **核心策略:** 在工单创建成功后的**异步阶段**执行，单次发送失败具备容错性，不影响主流程。

## 目录结构 (修正最终版)

```
app/ops/bpm/plugins/
└── order/
    ├── orderdto/
    │   └── biz_objects.go            # [改造] 确认工单返回DTO包含 is_cc 和 approval_status_for_me
    └── orderbiz/
        ├── order.go                  # [改造] 1.Service层仅做参数透传; 2.实现NewSendFeiShuAudit中的通知逻辑
        └── internal/
            └── orderdao/
                ├── obj.go            # [改造] 为每个列表接口定义专属的、包含 filter_by_role 的DAO参数结构体
                └── ext_order.go      # [改造] 在每个列表的DAO方法内部，实现 filter_by_role 到 SQL WHERE 子句的动态构建
```

## 整体逻辑和交互时序图 (修正最终版)

```mermaid
sequenceDiagram
    participant H as "Handler (order.go)"
    participant S as "Service (order.go)"
    participant DAO as "DAO (ext_order.go)"
    participant DB
    participant FeiShuSvc as "FeiShu Service"
    participant Log

    %% --- A) 列表筛选流程 (以我的审批列表为例) ---
    
    H->>S: GetMyAuditOrderWithSearch(ctx, param)
    note right of H: param.FilterByRole =<br>["TO_BE_APPROVED", "CC_TO_ME"]

    activate S
    S->>S: 创建专属DAO参数 MyAuditOrderDAOParam
    note right of S: 将前端DTO的FilterByRole原样赋给DAO参数
    S->>DAO: GetMyAuditOrdersWithRole(ctx, daoParam)
    deactivate S

    activate DAO
    DAO->>DAO: 在方法内部解析daoParam.FilterByRole
    DAO->>DAO: 动态构建WHERE子句
    DAO->>DB: 执行包含动态条件的复杂SQL查询
    DB-->>DAO: 返回带有is_cc, approval_status_for_me的结果
    DAO-->>S: return ordersWithRole, total, err
    deactivate DAO
    
    S->>S: 转换DAO结果为前端DTO
    S-->>H: 返回列表数据

    %% --- B) 异步通知流程 ---

    H->>S: go NewSendFeiShuAudit(ctx, order, ...)
    note right of H: 工单创建成功后，异步触发

    activate S
    S->>FeiShuSvc: SendMessage(审批人OpenID, 审批卡片)

    loop 遍历 ccUserInfos
        S->>S: 构建抄送通知卡片 (无链接)
        S->>FeiShuSvc: SendMessage(ccUser.CcOpenId, cardContent)
        alt 发送失败
            S->>Log: 记录错误日志
        end
    end
    deactivate S
```

## 模块化文件详解 (File-by-File Breakdown)

---
### `app/ops/bpm/plugins/order/orderdto/biz_objects.go`

a. **文件用途说明**: 定义Service层与Handler层交互时使用的数据传输对象(DTO)。

c. **改造内容**

#### **`OrderInfo` 结构体 (改造)**
- **用途:** 工单信息返回结构，**最终确认**包含 `IsCc` 和 `ApprovalStatusForMe` 字段。
```go
type OrderInfo struct {
    // ... 其他工单字段 ...
    IsCc                int    `json:"is_cc"`                  // 抄送状态 (0/1)
    ApprovalStatusForMe int    `json:"approval_status_for_me"` // 审批状态 (0-无, 1-已审, 2-将审, 3-待审)
}
```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/obj.go`

a. **文件用途说明**: 定义DAO层内部使用的数据结构，特别是各查询的专属参数。

c. **改造内容**

#### **专属DAO参数结构体 (改造)**
- **用途:** 为每个列表查询定义独立的参数结构体，直接包含前端的 `FilterByRole` 数组。
```go
// MyAuditOrderDAOParam 审批列表的专属DAO参数
type MyAuditOrderDAOParam struct {
    UserEmail    string
    Page         int
    PageSize     int
    SearchValue  string
    FilterByRole []string // 直接透传，供DAO内部解析
}

// MyOrdersDAOParam 进行中/已完结列表的专属DAO参数
type MyOrdersDAOParam struct {
    UserEmail    string
    Page         int
    PageSize     int
    FilterByRole []string // 直接透传，供DAO内部解析
}
```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go`

a. **文件用途说明**: **本次迭代的核心实现文件**。手动编写复杂DAO查询，并在此处实现筛选逻辑。

c. **函数/方法详解**

#### **`GetMyAuditOrdersWithRole`, `GetMyDoingOrdersWithRole` 等 (改造)**
- **用途:** 执行各自列表的SQL查询。
- **输入参数:** 接收各自专属的DAO参数（如 `*MyAuditOrderDAOParam`）。
- **核心实现逻辑:**
    1.  在每个函数内部，定义基础的SQL查询语句。
    2.  检查传入的 `param.FilterByRole` 数组是否为空。
    3.  若不为空，则根据数组内容，动态生成一段SQL `WHERE` 子句。
    4.  **示例(`GetMyDoingOrdersWithRole`):**
        *   如果 `param.FilterByRole` 为 `["APPLICANT"]`，则动态生成的子句为 `AND (o.proposer_email = ?)`。
        *   如果为 `["CC_TO_ME"]`，则为 `AND (occ.cc_email IS NOT NULL)`。
        *   如果为 `["APPLICANT", "CC_TO_ME"]` 或为空，则为 `AND (o.proposer_email = ? OR occ.cc_email IS NOT NULL)`。
    5.  将动态生成的子句拼接到基础SQL中，并准备好对应的参数，然后执行查询。

---
### `app/ops/bpm/plugins/order/orderbiz/order.go`

a. **文件用途说明**: 业务逻辑（Service）层，**职责简化**为参数透传和异步任务调度。

c. **函数/方法详解**

#### **`GetMyDoingOrdersByPage`, `GetDoneOrdersByPage`, `GetMyAuditOrderWithSearch` (改造)**
- **用途:** 将前端请求分发到正确的DAO方法。
- **实现流程:**
    1.  接收前端的DTO参数（如 `*orderdto.MyAuditOrderSearchParam`）。
    2.  创建对应的DAO参数实例（如 `*orderdao.MyAuditOrderDAOParam`）。
    3.  将DTO中的所有字段，**包括 `FilterByRole` 数组，原封不动地**赋值给DAO参数。
    4.  调用对应的 `biz.Dao.Get...` 方法，并传入准备好的DAO参数。
    5.  转换并返回结果。

#### **`NewSendFeiShuAudit` (改造)**
- **用途:** 在异步任务中，向审批人和所有抄送人发送飞书通知。
- **实现流程:**
    1.  向审批人发送通知。
    2.  循环遍历 `ccUserInfos` 数组。
    3.  为每个 `cc_open_id` 构建与申请人内容一致、且**不含链接**的通知卡片。
    4.  调用飞书服务发送。
    5.  **实现容错**：捕获单次发送失败的错误，记录详细日志，然后继续下一次循环。