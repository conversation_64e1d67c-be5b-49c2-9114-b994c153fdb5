# 后端开发步骤计划 (迭代三 & 四 - 最终完整版)

本计划基于我们所有最终确认的设计决策：**独立状态字段模型**、**以`email`为查询依据**、以及**每个列表接口拥有独立的DAO查询方法和参数**。

### **第一阶段：数据库与DAO层建设 (核心实现)**

**目标:** 在DAO层为每个列表的独立查询方法增加动态构建`WHERE`子句的能力，并使用各自专属的参数结构体。

*   **步骤 1.1: 确认 `tb_order_cc` 表结构**
    *   **任务:** 在数据库中确认 `tb_order_cc` 表已按需求文档创建，包含 `id`, `order_id`, `cc_open_id`, `cc_email` 等字段。
    *   **验证:** 通过SQL客户端执行 `DESC tb_order_cc;` 检查表结构。

*   **步骤 1.2: 定义或确认DAO层返回结构体**
    *   **文件:** `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/obj.go`
    *   **任务:** 确认 `OrderWithRoleInfo` 结构体已定义，并包含 `IsCc (int)` 和 `ApprovalStatusForMe (int)` 字段，用于接收SQL查询结果。
    *   **代码示例:**
        ```go
        type OrderWithRoleInfo struct {
            // ... 工单基本信息字段 ...
            IsCc                int `db:"is_cc"`
            ApprovalStatusForMe int `db:"approval_status_for_me"`
            // ... 可能还有其他如 stage_name 等专用字段 ...
        }
        ```
    *   **验证:** 代码审查，确认结构体字段与SQL查询的`SELECT`列名和类型匹配。

*   **步骤 1.3: 改造“我的审批”列表DAO查询及参数**
    *   **文件:** `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go` 和 `obj.go`
    *   **任务:**
        1.  **参数定义 (`obj.go`):** 确认或创建 `MyAuditOrderDAOParam` 结构体，它应包含 `UserEmail`, `Page`, `PageSize`, `SearchType`, `SearchValue` 以及前端传入的 `FilterByRole []string`。
        2.  **逻辑实现 (`ext_order.go`):** 修改 `GetMyAuditOrdersWithRole` 方法，使其接收 `*MyAuditOrderDAOParam` 作为参数。
        3.  在该方法内部，基于 `param.FilterByRole` 数组的内容，动态地修改 `WHERE` 子句。例如，如果 `FilterByRole` 只包含 `"CC_TO_ME"`，则`WHERE`条件应主要基于 `order_combined.is_cc_to_me = 1`。
    *   **验证:** 编写单元测试，模拟不同的 `MyAuditOrderDAOParam` 输入（特别是不同的 `FilterByRole` 组合），断言生成的最终SQL语句完全符合预期。

*   **步骤 1.4: 改造“进行中”和“已完结”列表DAO查询及参数**
    *   **文件:** `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go` 和 `obj.go`
    *   **任务:**
        1.  **参数定义 (`obj.go`):** 确认或创建 `MyOrdersDAOParam` 结构体（或两个更具体的 `MyDoingOrdersDAOParam`, `MyDoneOrdersDAOParam`），它应包含 `UserEmail`, `Page`, `PageSize`, 以及 `FilterByRole []string`。
        2.  **逻辑实现 (`ext_order.go`):** 修改 `GetMyDoingOrdersWithRole` 和 `GetMyDoneOrdersWithRole` 方法，使其接收各自的DAO参数。
        3.  在每个方法内部，根据 `param.FilterByRole` 的内容，动态构建 `WHERE` 子句中关于 `o.proposer_email` 和 `occ.cc_email` 的部分。
        *   **示例逻辑:** 如果 `FilterByRole` 只包含 `"APPLICANT"`，则 `WHERE` 子句中的 `OR` 条件应被简化为仅 `o.proposer_email = ?`。
    *   **验证:** 为这两个方法分别编写单元测试，验证传入不同 `FilterByRole` 组合时，SQL `WHERE` 子句的动态生成逻辑是正确的。

### **第二阶段：Service层逻辑实现 (参数传递与异步任务)**

**目标:** 简化Service层，使其仅负责参数传递、调用正确的DAO方法和执行异步通知。

*   **步骤 2.1: 改造三个列表Service方法 (最终简化版)**
    *   **文件:** `app/ops/bpm/plugins/order/orderbiz/order.go`
    *   **任务:** 修改 `GetMyDoingOrdersByPage`, `GetMyDoneOrdersByPage`, `GetMyAuditOrderWithSearch` 三个方法。
    *   **工作流:**
        1.  **接收前端请求参数:** 例如，`GetMyAuditOrderWithSearch` 接收 `*orderdto.MyAuditOrderSearchParam`。
        2.  **准备DAO参数:** 创建对应的、专属的DAO参数实例（如 `*orderdao.MyAuditOrderDAOParam`），并将前端DTO中的所有字段（包括 `FilterByRole` 数组）原封不动地赋值过去。
        3.  **调用专属DAO方法:** 将准备好的专属DAO参数传递给对应的DAO层函数（如 `biz.Dao.GetMyAuditOrdersWithRole(ctx, daoParam)`）。
        4.  **处理返回结果:** 将DAO层返回的 `[]OrderWithRoleInfo` 循环转换为前端需要的 `[]orderdto.OrderInfo` 结构体数组。
    *   **验证:** 对每个Service层方法进行集成测试，验证参数能够被正确地传递到DAO层，并且返回的数据结构正确。

*   **步骤 2.2: 实现异步消息通知逻辑 (迭代四)**
    *   **文件:** `app/ops/bpm/plugins/order/orderbiz/order.go`
    *   **任务:**
        1.  定位到 `NewSendFeiShuAudit` (或功能类似的异步处理函数)。
        2.  在其现有逻辑（如向审批人发通知）之后，增加一个新的循环，遍历 `ccUserInfos` 数组。
        3.  在循环内部，为每一个 `ccUser.CcOpenId` 调用飞书消息服务 `SendMessage`。
        4.  **消息内容:** 确保发送给抄送人的卡片内容与发送给申请人的一致，并且**不包含**任何链接。
        5.  **容错处理:** 使用 `try-catch` 等机制包裹 `SendMessage` 调用。如果发生错误，必须记录详细的错误日志（包含 `order_id` 和失败的 `cc_open_id`），但绝不能中断循环，也不能让整个异步任务失败。
    *   **验证:**
        1.  **代码审查:** 重点审查错误处理、日志记录的完备性。
        2.  **功能测试:** 创建一个带多个抄送人的工单，验证所有抄送人均能收到格式正确的、无链接的飞书通知。
        3.  **异常测试:** 创建工单时，在抄送人中混入一个无效的 `cc_open_id`，验证程序能够跳过该用户、记录错误日志，并成功通知其他所有有效的抄送人。

### **第三阶段：API接口联调与最终验收**

**目标:** 端到端验证所有功能，确保后端实现完全满足前端需求。

*   **步骤 3.1: 联调列表接口与筛选功能**
    *   **任务:** 与前端工程师协作，对“进行中”、“已完结”、“我的审批”三个列表接口进行完整联调。
    *   **验证清单:**
        *   **基础数据:** 在不使用筛选时，列表返回数据正常，`is_cc` 和 `approval_status_for_me` 字段值符合预期。
        *   **单条件筛选:** 对每个列表，分别使用 `filter_by_role` 数组传入单个条件（如 `["APPLICANT"]` 或 `["CC_TO_ME"]`），验证返回结果是否被正确过滤。
        *   **多条件筛选:** 传入多个条件的组合（如 `["APPLICANT", "CC_TO_ME"]`），验证返回结果是否为所选条件的**并集**。
        *   **空条件筛选:** 传入空的 `filter_by_role: []` 数组，验证是否等同于不筛选，返回全量数据。

*   **步骤 3.2: 验收工单创建与消息通知流程**
    *   **任务:** 与前端工程师一起，在联调环境中执行完整的“创建带抄送人工单”流程。
    *   **验证清单:**
        1.  **数据落库:** 创建成功后，检查 `tb_order` 和 `tb_order_cc` 表中的数据是否正确。
        2.  **消息触达:** 所有指定的抄送人都能在飞书客户端收到通知。
        3.  **消息正确性:** 收到的通知卡片内容、布局、格式与需求规格说明一致，且卡片内无任何可点击链接。
        4.  **系统日志:** 后台服务日志中没有出现与通知发送相关的、非预期的错误信息。