# 后端详细设计 (LLD) - 迭代二: 工单列表整合展示

### **与前一版本LLD (迭代一) 的主要区别**

1.  **核心逻辑变更 (Service层)**: 最大的变更是列表查询逻辑。我们采用**页面独立查询**模式，每个页面都有专属的查询方法，使用连表查询直接获取工单及角色信息。
2.  **DAO层扩展**: 为每个页面创建独立的连表查询方法。进行中工单和已完结工单使用新的连表查询，我的审批工单使用复杂的子查询，5.7不支持CTE。
3.  **DTO结构扩展**: 在`orderdto/biz_objects.go`中新增`OrderInfoWithRole`结构体和`RoleType`常量，在`orderdao/obj.go`中新增`OrderWithRoleInfo`结构体用于连表查询结果，`OrderInfo`中新增`IsCc`和`ApprovalStatusForMe`字段。
4.  **角色标识方式**: 直接返回具体的角色状态字段（`IsCc`, `ApprovalStatusForMe`）而不是单一的`role_type`字段，避免复杂的角色优先级计算。

### 项目结构与总体设计

迭代二的核心是改造工单列表API，使其能够展示与当前用户相关的所有工单（包括抄送给我的），并明确标识用户在每个工单中的角色状态。

我们采用**页面独立查询**策略：
- **我的审批工单**: 使用复杂的子查询，5.7不支持CTE，计算审批优先级和抄送状态
- **进行中工单**: 使用独立的连表查询方法，查询条件为`result = 0`，包含当前阶段信息
- **已完结工单**: 使用独立的连表查询方法，查询条件为`result != 0`，简化角色判断

### 目录结构 (迭代二变更点)

```
app/ops/bpm/plugins/
└── order/
    ├── orderdto/
    │   └── biz_objects.go            # [改造] 新增RoleType常量、OrderInfoWithRole结构体、参数结构体、OrderInfo新增字段
    └── orderbiz/
        ├── order.go                  # [改造] 重构三个页面的查询方法，使用结构化参数，简化角色转换
        └── internal/
            └── orderdao/
                ├── obj.go             # [改造] 新增OrderWithRoleInfo结构体，包含多个角色状态字段
                └── ext_order.go       # [改造] 新增独立的连表查询方法，使用复杂的子查询，5.7不支持CTE。
```

### 整体逻辑和交互时序图

下图展示了获取"进行中工单"列表的核心流程，使用了进行中工单特有的连表查询的设计。

```mermaid
sequenceDiagram
    participant H as "Handler"
    participant S as "Service (order.go)"
    participant DAO as "DAO (ext_order.go)"

    H->>S: "GetDoingOrdersByPage(ctx, param)"
    activate S

    Note over S: "使用特有的连表查询方法"
    S->>DAO: "GetMyDoingOrdersWithRole(ctx, daoParam)"
    
    Note over DAO: "执行连表查询"
    DAO->>DAO: "LEFT JOIN tb_stage, tb_order_cc"
    DAO->>DAO: "WHERE result = 0 AND (申请人 OR 抄送人)"
    DAO->>DAO: "在SQL层面计算角色标识和阶段信息"
    DAO-->>S: "ordersWithRole (包含 IsCc, StageName 等字段)"

    Note over S: "直接转换结果，无需复杂角色计算"
    S->>S: "调用 convertOrderWithRoleToOrderInfo"
    S->>S: "组装 OrderInfo 结果 (包含 IsCc, ApprovalStatusForMe)"
    
    S-->>H: "返回带具体角色状态字段的工单列表"
    deactivate S
```

### 模块化文件详解 (File-by-File Breakdown)

---
### `app/ops/bpm/plugins/order/orderdto/biz_objects.go`

a. **文件用途说明**: 定义Service层内部及与Handler层交互时使用的数据传输对象(DTO)。

c. **新增/改造内容**

#### **`RoleType` 常量 (新增)**
- **用途:** 定义用户角色类型的枚举值，主要用于内部逻辑处理，为迭代三筛选逻辑做准备。
- **使用场景:** 
  - 进行中工单和完结工单：`APPLICANT`（申请）、`CC_TO_ME`（抄送）
  - 审批工单：`TO_BE_APPROVED`（待审）、`WILL_BE_APPROVED`（将审）、`ALREADY_APPROVED`（已审）、`COMPLETED`（完结）、`CC_TO_ME`（抄送）
```go
// RoleType 定义了用户在工单中的角色
type RoleType string

const (
    RoleToBeApproved    RoleType = "TO_BE_APPROVED"    // [待审] - 审批工单
    RoleWillBeApproved  RoleType = "WILL_BE_APPROVED"  // [将审] - 审批工单
    RoleAlreadyApproved RoleType = "ALREADY_APPROVED"  // [已审] - 审批工单
    RoleCompleted       RoleType = "COMPLETED"         // [完结] - 审批工单
    RoleApplicant       RoleType = "APPLICANT"         // [申请] - 进行中工单、完结工单
    RoleCcToMe          RoleType = "CC_TO_ME"          // [抄送] - 所有页面通用
)
```

#### **参数结构体 (新增)**
- **用途:** 统一参数传递格式，遵循分层架构设计。
```go
// MyDoingOrderSearchParam 我的进行中工单搜索参数
type MyDoingOrderSearchParam struct {
    PageNum      int      `json:"page_num"`
    PageSize     int      `json:"page_size"`
    UserEmail    string   `json:"user_email"`
    FilterByRole []string `json:"filter_by_role"` // 迭代三预留
}

// MyDoneOrderSearchParam 我的已完结工单搜索参数
type MyDoneOrderSearchParam struct {
    PageNum      int      `json:"page_num"`
    PageSize     int      `json:"page_size"`
    UserEmail    string   `json:"user_email"`
    FilterByRole []string `json:"filter_by_role"` // 迭代三预留
}
```

#### **`OrderInfo` 结构体 (改造)**
- **用途:** 工单信息返回结构，新增具体的角色状态字段。
```go
type OrderInfo struct {
    OrderID             string
    OrderType           string
    OrderTypeName       string
    TotalStageNum       int64
    CurrentStage        int64
    CurrentStageName    string
    CurrentOperator     string
    ProposerEmail       string
    OpsOwnerEmail       string
    ApplyMsg            string
    Info                string
    Result              int64
    ResultDesc          string
    ResultMsg           string
    ApplyDatetime       string
    LastUpdatedDatetime string
    IsCc                int    `json:"is_cc"`                  // 抄送状态 (0/1)
    ApprovalStatusForMe int    `json:"approval_status_for_me"` // 审批状态优先级 (0-无关联, 1-已审, 2-将审, 3-待审)
}
```

#### **`OrderInfoWithRole` 结构体 (新增)**
- **用途:** 在Service层聚合数据时，临时封装工单信息及其关联的所有角色。
```go
// OrderInfoWithRole 用于在应用层聚合时，存储一个工单及其所有关联角色
type OrderInfoWithRole struct {
    Order interface{}       // 复用已有的DAO层结构体，使用interface{}避免循环导入
    Roles map[RoleType]bool // 使用map作为set，存储该用户与此工单的所有角色关系
}
```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/obj.go`

a. **文件用途说明**: 定义DAO层内部使用的数据结构。

c. **新增内容**

#### **`OrderWithRoleInfo` 结构体 (新增)**
- **用途:** 连表查询结果结构体，包含工单信息和角色状态字段。
```go
// OrderWithRoleInfo 连表查询结果结构体，包含工单信息和角色信息
type OrderWithRoleInfo struct {
    // 工单基本信息
    Id            int       `db:"id"`
    OrderId       string    `db:"order_id"`
    OrderType     string    `db:"order_type"`
    Info          string    `db:"info"`
    Exigency      int       `db:"exigency"`
    ApplyMsg      string    `db:"apply_msg"`
    ProposerEmail string    `db:"proposer_email"`
    OpsOwnerEmail string    `db:"ops_owner_email"`
    TotalStageNum int       `db:"total_stage_num"`
    CurrentStage  int       `db:"current_stage"`
    Result        int       `db:"result"`
    ResultMsg     string    `db:"result_msg"`
    IsDel         int       `db:"is_del"`
    Ctime         time.Time `db:"ctime"`
    Mtime         time.Time `db:"mtime"`

    // 通用角色字段
    IsCc                int    `db:"is_cc"`                  // 抄送状态 (0/1)
    ApprovalStatusForMe int    `db:"approval_status_for_me"` // 审批状态优先级 (0-无关联, 1-已审, 2-将审, 3-待审)
    
    // 进行中工单专用字段
    StageName             string `db:"stage_name"`                // 当前阶段名称
}
```

---
### `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go`

a. **文件用途说明**: 手动编写的、与`tb_order`表相关的复杂DAO查询。

c. **函数/方法详解**

#### **`GetMyDoingOrdersWithRole` (新增)**
- **用途:** 获取用户的进行中工单及角色信息。
- **输入参数:** `ctx context.Context`, `param *MyOrdersDAOParam`
- **输出数据结构:** `[]OrderWithRoleInfo`, `int64`, `error`
- **核心特点:**
  - 查询条件：`o.result = 0` (进行中工单)
  - 包含当前阶段名称 `stage_name`
  - 简化的角色判断：只判断申请人和抄送人
  - 使用 `cc_email` 进行抄送查询

#### **`GetMyDoneOrdersWithRole` (新增)**
- **用途:** 获取用户的已完结工单及角色信息。
- **输入参数:** `ctx context.Context`, `param *MyOrdersDAOParam`
- **输出数据结构:** `[]OrderWithRoleInfo`, `int64`, `error`
- **核心特点:**
  - 查询条件：`o.result != 0` (已完结工单)
  - 简化的连表查询
  - 只关注申请人和抄送人角色

#### **`GetMyAuditOrdersWithRole` (改造)**
- **用途:** 获取用户的审批工单及角色信息，支持复杂搜索。
- **核心特点:**
  - 使用复杂的CTE查询计算审批优先级
  - 支持标题、时间范围、工单类型等多种搜索条件
  - 计算 `approval_status_for_me` 字段 (0-无关联, 1-已审, 2-将审, 3-待审)
  - 包含抄送状态判断

---
### `app/ops/bpm/plugins/order/orderbiz/order.go`

a. **文件用途说明**: 业务逻辑（Service）层，实现工单列表查询的核心逻辑。

c. **函数/方法详解**

#### **`GetDoingOrdersByPage`, `GetDoneOrdersByPage` (改造)**
- **用途:** 对外暴露的列表查询接口，使用独立的连表查询方法。
- **参数变更:** 使用结构化参数 `*orderdto.MyDoingOrderSearchParam` 和 `*orderdto.MyDoneOrderSearchParam`
- **实现流程:**
```mermaid
flowchart TD
    A["Start"] --> B["调用独立的连表查询方法"];
    B --> C["GetMyDoingOrdersWithRole 或 GetMyDoneOrdersWithRole"];
    C --> D["直接转换查询结果"];
    D --> E["组装 OrderInfo 结果 (包含 IsCc, ApprovalStatusForMe)"];
    E --> F["End"];
```

#### **`GetMyAuditOrderWithSearch` (保持复杂逻辑)**
- **用途:** 审批工单的复杂搜索逻辑，支持多种筛选条件。
- **实现:** 调用 `biz.Dao.GetMyAuditOrdersWithRole(ctx, daoParam)`

#### **`convertOrderWithRoleToOrderInfo` (简化)**
- **用途:** 将DAO层的查询结果转换为业务层的返回结构。
- **实现特点:**
  - 直接映射字段，无需复杂的角色优先级计算
  - 保留原有的 `IsCc` 和 `ApprovalStatusForMe` 字段
  - 添加 `CurrentStageName` 等新字段

## 实际实现的关键设计决策

1.  **简化角色处理**: 不使用复杂的角色优先级计算，直接返回具体的角色状态字段，让前端根据需要显示。
2.  **分页面优化**: 每个页面的查询逻辑独立，进行中工单包含阶段信息，已完结工单简化查询。
3.  **审批工单特殊处理**: 使用复杂的CTE查询支持高级搜索功能和优先级计算。
4.  **使用cc_email**: 直接使用邮箱进行抄送查询，避免额外的用户信息获取。
5.  **结构化参数**: 遵循分层架构设计，使用DTO进行参数传递。

## 迭代演进依据

这份详细设计确保了系统能够平滑地进行后续迭代：

1.  **性能与规范的平衡**: 使用连表查询提高性能，同时保持代码的清晰性和可维护性。
2.  **模块化和解耦合**: 每个页面都有独立的查询方法，修改一个页面不会影响其他页面。
3.  **为迭代三准备就绪**: 参数结构体中已预留`FilterByRole`字段，为角色筛选功能做好准备。
4.  **清晰的职责分离**: 审批工单保持复杂搜索功能，进行中和已完结工单专注于简单的角色聚合。
