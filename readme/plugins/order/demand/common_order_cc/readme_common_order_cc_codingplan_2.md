# 项目迭代二：工单列表整合与角色展示

#### **总览**

本次迭代的核心目标是改造工单列表API（"进行中"、"已完结"、"我的审批"），使其能够整合展示当前用户作为"申请人"、"审批人"以及"抄送人"的所有相关工单。我们采用**页面独立查询**策略，每个页面都有专属的查询方法，确保功能的独立性和可维护性。实际实现中，我们选择了直接返回具体的角色状态字段而非单一的role_type字段，简化了角色计算逻辑。

#### **最终代码目录结构 (迭代二变更点)**

```
app/ops/bpm/plugins/
└── order/
    ├── orderdto/
    │   └── biz_objects.go            # [改造] 新增RoleType常量、OrderInfoWithRole结构体、参数结构体、OrderInfo新增字段
    └── orderbiz/
        ├── order.go                  # [改造] 重构进行中和已完结工单查询方法，使用结构化参数，简化角色转换
        └── internal/
            └── orderdao/
                ├── obj.go             # [改造] 新增OrderWithRoleInfo结构体，包含多个角色状态字段
                └── ext_order.go       # [改造] 新增独立的连表查询方法，包含复杂CTE查询
```

#### **开发流程Mermaid图**

```mermaid
flowchart TD
    subgraph Step 1: 数据结构与常量定义
        A["在 orderdto/biz_objects.go 中定义 RoleType 常量"] --> B["定义 OrderInfoWithRole 内部DTO"];
        B --> C["在 OrderInfo 中新增 IsCc、ApprovalStatusForMe 字段"];
        C --> D["定义参数结构体 MyDoingOrderSearchParam, MyDoneOrderSearchParam"];
    end
    subgraph " "
        direction LR
        D -- 可验证 --> V1[验证: 项目可以无错误地编译];
    end

    subgraph Step 2: 实现DAO层独立查询方法
        E["在 obj.go 中新增 OrderWithRoleInfo 结构体 (包含多种角色字段)"] --> F["在 ext_order.go 中新增 GetMyDoingOrdersWithRole"];
        F --> G["在 ext_order.go 中新增 GetMyDoneOrdersWithRole"];
        G --> H["改造 GetMyAuditOrdersWithRole 支持CTE查询"];
    end
    subgraph " "
        direction LR
        H -- 可验证 --> V2[验证: 三个DAO方法可通过单元测试验证其功能正确性];
    end
    
    subgraph Step 3: 简化Service层角色转换逻辑
        I["在 orderbiz/order.go 中简化 convertOrderWithRoleToOrderInfo 方法"];
        I --> J["直接映射角色字段，移除复杂的优先级计算"];
    end
    subgraph " "
        direction LR
        J -- 可验证 --> V3[验证: 角色转换逻辑可通过单元测试验证];
    end
    
    subgraph Step 4: 重构并集成列表API
        K["重构 GetDoingOrdersByPage 使用独立查询"] --> L["重构 GetDoneOrdersByPage 使用独立查询"];
        L --> M["改造 GetMyAuditOrderWithSearch 使用新的CTE查询"];
        M --> N["更新Controller层返回新增字段"];
    end
    subgraph " "
        direction LR
        N -- 可验证 --> V4[验证: 调用三个工单列表API, 能正确返回包含 IsCc、ApprovalStatusForMe 等字段的列表];
    end
    
    Step1 --> Step2 --> Step3 --> Step4;
```

---

### **渐进式小步迭代式开发与集成步骤**

#### **第一步：定义数据结构与常量 (DTO Layer)**

此步骤为后续的业务逻辑实现准备好必要的数据容器和类型定义。

1.  **定义角色类型常量和内部DTO**:
    *   **任务**: 打开 `app/ops/bpm/plugins/order/orderdto/biz_objects.go` 文件。
        1.  在文件中新增 `RoleType` 常量定义，包含 `TO_BE_APPROVED`, `ALREADY_APPROVED`, `APPLICANT`, `CC_TO_ME` 四个值。
        2.  新增 `OrderInfoWithRole` 内部结构体，用于在Service层临时存储工单及其所有角色。
        3.  **重要变更**: 在 `OrderInfo` 结构体中新增 `IsCc` 和 `ApprovalStatusForMe` 字段。
        4.  新增参数结构体 `MyDoingOrderSearchParam` 和 `MyDoneOrderSearchParam`。
            ```go
            // RoleType 定义了用户在工单中的角色
            type RoleType string

            const (
                RoleToBeApproved    RoleType = "TO_BE_APPROVED"
                RoleAlreadyApproved RoleType = "ALREADY_APPROVED"
                RoleApplicant       RoleType = "APPLICANT"
                RoleCcToMe          RoleType = "CC_TO_ME"
            )
            
            // OrderInfo 结构体新增字段
            type OrderInfo struct {
                // ... 原有字段
                IsCc                int    `json:"is_cc"`                  // 抄送状态 (0/1)
                ApprovalStatusForMe int    `json:"approval_status_for_me"` // 审批状态优先级 (0-无关联, 1-已审, 2-将审, 3-待审)
            }

            // MyDoingOrderSearchParam 我的进行中工单搜索参数
            type MyDoingOrderSearchParam struct {
                PageNum      int      `json:"page_num"`
                PageSize     int      `json:"page_size"`
                UserEmail    string   `json:"user_email"`
                FilterByRole []string `json:"filter_by_role"` // 迭代三预留
            }

            // MyDoneOrderSearchParam 我的已完结工单搜索参数
            type MyDoneOrderSearchParam struct {
                PageNum      int      `json:"page_num"`
                PageSize     int      `json:"page_size"`
                UserEmail    string   `json:"user_email"`
                FilterByRole []string `json:"filter_by_role"` // 迭代三预留
            }
            ```
    *   **验证**: 保存文件后，确保项目可以无错误地编译。

---

#### **第二步：实现DAO层独立查询方法 (DAO Layer)**

此步骤为每个页面创建专属的连表查询方法，确保查询逻辑的独立性。

1.  **新增连表查询结果结构体**:
    *   **任务**: 打开 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/obj.go` 文件，新增 `OrderWithRoleInfo` 结构体。
        ```go
        // OrderWithRoleInfo 连表查询结果结构体，包含工单信息和多种角色状态字段
        type OrderWithRoleInfo struct {
            // 工单基本信息
            Id            int       `db:"id"`
            OrderId       string    `db:"order_id"`
            OrderType     string    `db:"order_type"`
            Info          string    `db:"info"`
            Exigency      int       `db:"exigency"`
            ApplyMsg      string    `db:"apply_msg"`
            ProposerEmail string    `db:"proposer_email"`
            OpsOwnerEmail string    `db:"ops_owner_email"`
            TotalStageNum int       `db:"total_stage_num"`
            CurrentStage  int       `db:"current_stage"`
            Result        int       `db:"result"`
            ResultMsg     string    `db:"result_msg"`
            IsDel         int       `db:"is_del"`
            Ctime         time.Time `db:"ctime"`
            Mtime         time.Time `db:"mtime"`
            
            // 角色信息 (已完结工单使用)
            IsApplicant       bool `db:"is_applicant"`        // 是否为申请人
            IsToBeApproved    bool `db:"is_to_be_approved"`   // 是否为待审批人
            IsAlreadyApproved bool `db:"is_already_approved"` // 是否为已审批人
            IsCcToMe          bool `db:"is_cc_to_me"`         // 是否为抄送人
            
            // 通用角色字段
            IsCc                int    `db:"is_cc"`                  // 抄送状态 (0/1)
            ApprovalStatusForMe int    `db:"approval_status_for_me"` // 审批状态优先级
            
            // 进行中工单专用字段
            StageName             string `db:"stage_name"`                // 当前阶段名称
            IsCurrentStageAuditor int    `db:"is_current_stage_auditor"`  // 是否当前阶段审批人
        }
        ```

2.  **新增"进行中工单"独立查询方法**:
    *   **任务**: 打开 `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/ext_order.go` 文件，新增 `GetMyDoingOrdersWithRole` 方法。
        *   使用连表查询 `tb_order`, `tb_stage`, `tb_order_cc`
        *   查询条件：`o.result = 0` (进行中工单)
        *   简化角色判断：只关注申请人和抄送人
        *   包含当前阶段名称
        *   使用 `cc_email` 进行抄送查询
    *   **验证**: 编写单元测试验证查询逻辑的正确性。

3.  **新增"已完结工单"独立查询方法**:
    *   **任务**: 在同一文件中新增 `GetMyDoneOrdersWithRole` 方法。
        *   与进行中工单查询类似，但查询条件为 `o.result != 0` (已完结工单)
        *   更加简化的连表查询，只关注申请人和抄送人
    *   **验证**: 编写单元测试验证查询逻辑的正确性。

4.  **改造"我的审批工单"查询方法**:
    *   **任务**: 在同一文件中改造 `GetMyAuditOrdersWithRole` 方法。
        *   使用复杂的CTE查询计算审批优先级
        *   支持多种搜索条件（标题、时间范围、工单类型等）
        *   计算 `approval_status_for_me` 字段
        *   包含抄送状态判断
    *   **验证**: 编写单元测试验证复杂搜索逻辑的正确性。

---

#### **第三步：简化Service层角色转换逻辑 (Service Layer - Core Logic)**

此步骤简化角色处理逻辑，直接映射字段而非复杂计算。

1.  **简化 `convertOrderWithRoleToOrderInfo` 方法**:
    *   **任务**: 在 `app/ops/bpm/plugins/order/orderbiz/order.go` 中，简化现有的转换方法。
        *   直接映射 DAO 层查询结果到 DTO 字段
        *   保留 `IsCc` 和 `ApprovalStatusForMe` 字段
        *   添加 `CurrentStageName` 等新字段
        *   **关键变更**: 移除复杂的角色优先级计算逻辑
        ```go
        // convertOrderWithRoleToOrderInfo 转换OrderWithRoleInfo为OrderInfo
        func (biz *OrderBiz) convertOrderWithRoleToOrderInfo(ordersWithRole []orderdao.OrderWithRoleInfo) []orderdto.OrderInfo {
            var orderInfos []orderdto.OrderInfo

            for _, orderWithRole := range ordersWithRole {
                orderInfo := orderdto.OrderInfo{
                    OrderID:             orderWithRole.OrderId,
                    OrderType:           orderWithRole.OrderType,
                    OrderTypeName:       config.OrderTypeConfMap[orderWithRole.OrderType].Name,
                    TotalStageNum:       int64(orderWithRole.TotalStageNum),
                    CurrentStage:        int64(orderWithRole.CurrentStage),
                    CurrentStageName:    orderWithRole.StageName,  // 新增字段
                    Result:              int64(orderWithRole.Result),
                    ResultDesc:          orderdto.OrderResults[orderWithRole.Result],
                    ProposerEmail:       orderWithRole.ProposerEmail,
                    OpsOwnerEmail:       orderWithRole.OpsOwnerEmail,
                    ApplyMsg:            orderWithRole.ApplyMsg,
                    Info:                orderWithRole.Info,
                    ApplyDatetime:       orderWithRole.Ctime.Format(aixtime.DefFmt),
                    LastUpdatedDatetime: orderWithRole.Mtime.Format(aixtime.DefFmt),
                    IsCc:                orderWithRole.IsCc,                // 直接映射
                    ApprovalStatusForMe: orderWithRole.ApprovalStatusForMe, // 直接映射
                }

                orderInfos = append(orderInfos, orderInfo)
            }

            return orderInfos
        }
        ```
    *   **验证**: 对转换逻辑进行单元测试，确保字段映射正确。

---

#### **第四步：重构并集成列表API (API Integration)**

此步骤将新构建的查询能力应用到现有的API接口上，完成最终的整合。

1.  **重构 `GetDoingOrdersByPage` 和 `GetDoneOrdersByPage`**:
    *   **任务**: 修改 `orderbiz/order.go` 中的这两个方法。
        *   修改方法签名，使用结构化参数
        *   调用对应的独立连表查询方法
        *   使用简化的转换逻辑
        *   确保返回包含 `IsCc` 和 `ApprovalStatusForMe` 字段
    *   **验证**: 启动服务，调用相应的API接口，验证返回的列表格式正确。

2.  **改造 `GetMyAuditOrderWithSearch`**:
    *   **任务**: 修改审批工单查询方法。
        *   使用新的 `GetMyAuditOrdersWithRole` DAO方法
        *   保持复杂搜索功能不变
        *   确保返回包含优先级字段
    *   **验证**: 确认审批工单页面的所有搜索功能正常工作。

3.  **更新Controller层参数传递和返回结构**:
    *   **任务**: 修改 `order.go` 中的Controller方法。
        *   构建 `MyDoingOrderSearchParam` 和 `MyDoneOrderSearchParam` 参数
        *   传递给对应的Service方法
        *   **重要**: 确保返回的 protobuf 结构包含新增字段
        ```go
        // 在Controller的返回中添加新字段
        resp.Orders = append(resp.Orders, &orderproto.Order{
            OrderId:             item.OrderID,
            OrderType:           item.OrderType,
            OrderTypeName:       item.OrderTypeName,
            // ... 其他字段
            IsCc:                int32(item.IsCc),                // 新增字段
            ApprovalStatusForMe: int32(item.ApprovalStatusForMe), // 新增字段
        })
        ```
    *   **验证**: 确保API接口的参数传递正确，返回字段完整。

---

### **关键设计决策说明 (基于实际实现)**

1.  **简化角色处理**: 直接返回具体的角色状态字段（`IsCc`, `ApprovalStatusForMe`），让前端根据业务逻辑决定如何展示，避免后端复杂的优先级计算。
2.  **页面差异化查询**: 每个页面的查询逻辑独立，进行中工单包含阶段信息，已完结工单简化查询，审批工单保持复杂搜索。
3.  **使用cc_email**: 直接使用邮箱进行抄送查询，简化数据获取流程。
4.  **CTE查询优化**: 审批工单使用复杂的CTE查询支持高级搜索功能和优先级计算。
5.  **结构化参数**: 遵循分层架构设计，使用DTO进行参数传递。

### **实际实现与原计划的主要差异**

1.  **角色处理方式**: 原计划使用复杂的角色优先级计算，实际采用了更直接的多字段返回方式。
2.  **返回结构**: 不返回单一的 `role_type` 字段，而是返回多个具体的角色状态字段。
3.  **查询复杂度**: 实际实现中，不同页面的查询复杂度差异更大，审批工单使用了CTE查询。
4.  **转换逻辑**: Service层的转换逻辑更加简单，直接映射字段而非复杂计算。

### **迭代演进准备**

1.  **为迭代三准备**: 参数结构体中已预留 `FilterByRole` 字段，角色状态字段支持灵活的前端筛选。
2.  **性能优化**: 不同页面的查询逻辑独立，减少了不必要的复杂查询。
3.  **代码解耦**: 每个页面的查询逻辑完全独立，便于后续维护和扩展。
4.  **前端友好**: 返回具体的角色状态字段，前端可以根据业务需求灵活处理。
