package auth

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"aix.zhhainiao.com/aixpublic/server/core/ctxlog"
	"aix.zhhainiao.com/aixpublic/server/core/ctxutil"
	"aix.zhhainiao.com/app/ops/bpm/core/errors"
	"aix.zhhainiao.com/app/ops/bpm/core/utils"
	"aix.zhhainiao.com/app/ops/bpm/plugins/auth/authproto"
	"aix.zhhainiao.com/app/ops/bpm/plugins/auth/authutil"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/config"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/orderdto"
)

// OASidToken 根据 oa sid 获取token，用于oa跳转登录
func (ctrl *AuthController) OASidToken(ctx context.Context, req *authproto.OASidTokenReq) (resp *authproto.OASidTokenResp, err error) {
	resp = new(authproto.OASidTokenResp)
	// 验证oa sid 码
	userOaInfo, err := ctrl.Biz.VerifyOASid(ctx, req.Sid)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("sys interface OAToken VerifyOASid err = %v", err)
		return
	}
	// 生成token
	tokenField := authutil.GetConfig().WfoTokenField
	token, expireSec, err := ctrl.Biz.GenerateToken(ctx, tokenField, userOaInfo.Uid, userOaInfo.Username, 7)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("sys interface OAToken GenerateToken err = %v", err)
		return

	}
	resp.Token = token
	resp.UserEmail = userOaInfo.Uid
	resp.UserName = userOaInfo.Username
	resp.OaSid = req.Sid
	resp.ExpireSec = int32(expireSec)
	return
}

// OAPwdToken 根据 oa账号 和 加密过的oa密码 获取token，用于对外接口服务
func (ctrl *AuthController) OAPwdToken(ctx context.Context, req *authproto.OAPwdTokenReq) (resp *authproto.OAPwdTokenResp, err error) {
	resp = new(authproto.OAPwdTokenResp)
	// 密码解密
	AESKey := authutil.GetConfig().PwdAESKey
	EncryptedBytePwd, err := base64.StdEncoding.DecodeString(req.EncryptedPwd)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("OAPwdToken base64 DecodeString err = %v", err)
	}
	decryptedBytePwd, err := utils.AesDecryptCBC(ctx, EncryptedBytePwd, []byte(AESKey))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("OAPwdToken AesDecryptCBC = %v", err)
	}
	pwd := string(decryptedBytePwd)
	// 获取 ldap sid
	sid, err := ctrl.Biz.GetSid(ctx, req.UserEmail, pwd)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetToken GetSid err = %v", err)
		return
	}
	// 验证sid
	userOaInfo, err := ctrl.Biz.VerifyOASid(ctx, sid)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("sys interface OAToken VerifyOASid err = %v", err)
		return
	}
	// 生成token
	tokenField := authutil.GetConfig().WfoAPITokenField
	token, _, err := ctrl.Biz.GenerateToken(ctx, tokenField, userOaInfo.Uid, userOaInfo.Username, 7)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("sys interface OAToken GenerateToken err = %v", err)
		return
	}
	resp.Token = token
	return
}

// EncryptedPwdToken 密码加密接口
func (ctrl *AuthController) EncryptedPwdToken(ctx context.Context, req *authproto.EncryptedPwdTokenReq) (resp *authproto.EncryptedPwdTokenResp,
	err error) {
	resp = new(authproto.EncryptedPwdTokenResp)
	AESKey := authutil.GetConfig().PwdAESKey
	encryptedByteKey, err := utils.AesEncryptCBC(ctx, []byte(req.Pwd), []byte(AESKey))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("EncryptedPwdToken AesEncryptCBC err = %v", err)
	}
	resp.EncryptedPwd = base64.StdEncoding.EncodeToString(encryptedByteKey)
	return
}

func (ctrl *AuthController) AkSkToken(ctx context.Context, req *authproto.AkSkTokenReq) (resp *authproto.AkSkTokenResp, err error) {
	resp = new(authproto.AkSkTokenResp)
	isVerify := ctrl.Biz.VerifySign(ctx, req.Sign, req.AppId)
	if !isVerify {
		ctxlog.WithCtx(ctx).Errorf("Verify as/sk fail!")
		resp.Token = ""
		return resp, errors.VerifyAKSKError
	}

	h := md5.New()
	h.Write([]byte(req.Sign + fmt.Sprint(time.Now().Unix())))
	hashID := hex.EncodeToString(h.Sum(nil))[8:24]

	// 生成token
	tokenField := authutil.GetConfig().WfoAPPTokenField
	token, err := ctrl.Biz.AKSKGenerateToken(ctx, tokenField, req.AppId, hashID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("sys interface OAToken GenerateToken err = %v", err)
		return
	}
	resp.Token = token
	return
}
func (ctrl *AuthController) NewOrRefreshLongTermToken(ctx context.Context, req *authproto.NewOrRefreshLongTermTokenReq) (
	resp *authproto.NewOrRefreshLongTermTokenResp, err error) {
	resp = new(authproto.NewOrRefreshLongTermTokenResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	// 处理 apply_info
	applyInfo := make(map[string]string)
	applyInfo["proposer_email"] = userEmail
	applyInfo["apply_msg"] = req.ApplyMsg
	applyInfo["valid_day"] = strconv.Itoa(int(req.ValidDay))

	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]
	applyInfo["order_id"] = orderID

	applyInfoByte, err := json.Marshal(applyInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewOrRefreshLongTermToken Marshal err = %v", err)
	}

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.NewOrRefreshLongTermTokenOrderTypeConf.ID,
		Info:          string(applyInfoByte),
		ApplyMsg:      req.ApplyMsg,
		ProposerEmail: userEmail,
	}
	err = ctrl.OrderBiz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go ctrl.OrderBiz.NewCreateStage(order, nil)

	return
}

func (ctrl *AuthController) ViewLongTermToken(ctx context.Context, req *authproto.ViewLongTermTokenReq) (resp *authproto.ViewLongTermTokenResp, err error) {
	resp = new(authproto.ViewLongTermTokenResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	// 判断是否有用户是否有长效token
	isExist, err := ctrl.Biz.ExistLongTermToken(ctx, userEmail)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("ViewLongTermToken ExistLongTermToken err = %v", err)
		return
	}
	if !isExist {
		err = authutil.NonexistentLongtermToken
		return
	}

	// 处理 apply_info
	applyInfo := make(map[string]string)
	applyInfo["proposer_email"] = userEmail
	applyInfoByte, err := json.Marshal(applyInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewOrRefreshLongTermToken Marshal err = %v", err)
	}
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]
	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.ViewLongtermTokenTypeConf.ID,
		Info:          string(applyInfoByte),
		ApplyMsg:      fmt.Sprintf("查看 %s 的长效token", userEmail),
		ProposerEmail: userEmail,
	}
	err = ctrl.OrderBiz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("ViewLongTermToken NewCreateOrder err=%v", err)
		return
	}
	go ctrl.OrderBiz.NewCreateStage(order, nil)
	return
}
