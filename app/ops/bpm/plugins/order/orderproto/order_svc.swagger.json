{"swagger": "2.0", "info": {"title": "app/ops/bpm/plugins/order/orderproto/order_svc.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/cdn/domain/create": {"post": {"summary": "华为cdn加速域名创建", "description": "华为cdn加速域名创建", "operationId": "CdnDomainCreate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoCdnDomainCreateResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoCdnDomainCreateReq"}}], "tags": ["工单"]}}, "/domain/resolve": {"post": {"summary": "腾讯云域名解析", "description": "腾讯云域名解析", "operationId": "DomainResolve", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoDomainResolveResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoDomainResolveReq"}}], "tags": ["工单"]}}, "/order/approval": {"post": {"summary": "页面审批", "description": "页面审批", "operationId": "OrderApproval", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoOrderApprovalResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoOrderApprovalReq"}}], "tags": ["工单"]}}, "/order/cloud/mysql/buy": {"post": {"summary": "云 Mysql 购买工单", "description": "云 Mysql 购买工单", "operationId": "BuyCloudMysql", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoBuyCloudMysqlResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoBuyCloudMysqlReq"}}], "tags": ["工单"]}}, "/order/cloud/redis/buy": {"post": {"summary": "云Redis购买工单", "description": "云Redis购买工单", "operationId": "BuyCloudRedis", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoBuyCloudRedisResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoBuyCloudRedisReq"}}], "tags": ["工单"]}}, "/order/cloud/server/buy": {"post": {"summary": "云服务器购买工单", "description": "云服务器购买工单", "operationId": "BuyCloudServer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoBuyCloudServerResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoBuyCloudServerReq"}}], "tags": ["工单"]}}, "/order/cmdb/resource/update-service-owner": {"post": {"summary": "更新资源服务负责人", "description": "更新资源服务负责人", "operationId": "UpdateCMDBResourceServiceOwner", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoUpdateCMDBResourceServiceOwnerResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoUpdateCMDBResourceServiceOwnerReq"}}], "tags": ["工单"]}}, "/order/common": {"post": {"summary": "通用工单", "description": "通用工单", "operationId": "CommonOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoCommonOrderResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoCommonOrderReq"}}], "tags": ["工单"]}}, "/order/detail": {"post": {"summary": "获取工单详情", "description": "获取工单详情", "operationId": "GetOrderDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoGetOrderDetailResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoGetOrderDetailReq"}}], "tags": ["工单"]}}, "/order/domain/buy": {"post": {"summary": "购买域名工单", "description": "购买域名工单", "operationId": "DomainOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoDomainOrderResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoDomainOrderReq"}}], "tags": ["工单"]}}, "/order/flow-audit/turn": {"post": {"summary": "审批人变更", "description": "审批人变更", "operationId": "TurnFlowAudit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoTurnFlowAuditResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoTurnFlowAuditReq"}}], "tags": ["工单"]}}, "/order/model/cost/apportion-conf": {"post": {"summary": "成本模型分摊设置工单", "description": "成本模型分摊设置工单", "operationId": "CostModelApportionConf", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoCostModelApportionConfResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoCostModelApportionConfReq"}}], "tags": ["工单"]}}, "/order/model/delete-node": {"post": {"summary": "模型节点删除工单", "description": "模型节点删除工单", "operationId": "DeleteModelNode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoDeleteModelNodeResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoDeleteModelNodeReq"}}], "tags": ["工单"]}}, "/order/model/drag-node": {"post": {"summary": "模型节点移动工单", "description": "模型节点移动工单", "operationId": "DragModelNode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoDragModelNodeResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoDragModelNodeReq"}}], "tags": ["工单"]}}, "/order/model/modify-node": {"post": {"summary": "模型节点修改工单", "description": "模型节点修改工单", "operationId": "ModifyModelNode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoModifyModelNodeResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoModifyModelNodeReq"}}], "tags": ["工单"]}}, "/order/model/new-child-node": {"post": {"summary": "模型子节点新增工单", "description": "模型子节点新增工单", "operationId": "NewModelChildNode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoNewModelChildNodeResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoNewModelChildNodeReq"}}], "tags": ["工单"]}}, "/order/model/new-node": {"post": {"summary": "模型节点新增工单", "description": "模型节点新增工单", "operationId": "NewModelNode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoNewModelNodeResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoNewModelNodeReq"}}], "tags": ["工单"]}}, "/order/my-audit": {"post": {"summary": "获取我的审批工单", "description": "获取我的审批工单", "operationId": "GetMyAuditOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoGetMyAuditOrderResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoGetMyAuditOrderReq"}}], "tags": ["工单"]}}, "/order/my-doing": {"post": {"summary": "获取进行中的工单", "description": "获取进行中的工单", "operationId": "GetMyDoingOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoGetMyDoingOrderResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoGetMyDoingOrderReq"}}], "tags": ["工单"]}}, "/order/my-done": {"post": {"summary": "获取已完结工单", "description": "获取已完结工单", "operationId": "GetMyDoneOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoGetMyDoneOrderResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoGetMyDoneOrderReq"}}], "tags": ["工单"]}}, "/order/resouce/delete": {"post": {"summary": "云资源销毁", "description": "云资源销毁", "operationId": "ResouceDelete", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoResouceDeleteResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoResouceDeleteReq"}}], "tags": ["工单"]}}, "/order/server/jump/impower": {"post": {"summary": "服务器跳板机账号授权", "description": "服务器跳板机账号授权", "operationId": "ServerJumpImpower", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoServerJumpImpowerResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoServerJumpImpowerReq"}}], "tags": ["工单"]}}, "/order/server/ks/apply": {"post": {"summary": "金三云服务器申请", "description": "金三云服务器申请", "operationId": "KsServerApply", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoKsServerApplyResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoKsServerApplyReq"}}], "tags": ["工单"]}}, "/order/token/apply": {"post": {"summary": "k8s token 申请", "description": "k8s token 申请", "operationId": "TokenApply", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoTokenApplyResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoTokenApplyReq"}}], "tags": ["工单"]}}, "/order/validate-user": {"post": {"summary": "用户校验请求", "description": "用户校验请求", "operationId": "ValidateUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/orderprotoValidateUserResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/orderprotoValidateUserReq"}}], "tags": ["工单"]}}}, "definitions": {"msgcommonCommon": {"type": "object", "properties": {"uid": {"type": "string", "default": "\"\"", "description": "用户标识，默认为空", "title": "用户标识，默认为空"}, "token": {"type": "string", "default": "\"\"", "description": "用户验证token，默认为空", "title": "用户验证token，默认为空"}, "app_id": {"type": "string", "default": "\"\"", "description": "接入的APP id，默认为空", "title": "接入的APP id，默认为空"}, "request_id": {"type": "string", "default": "\"\"", "description": "上游请求ID，默认为空", "title": "上游请求ID，默认为空"}, "channel": {"type": "string", "default": "\"\"", "description": "客户端渠道，默认为空", "title": "客户端渠道，默认为空"}, "version": {"type": "string", "default": "\"\"", "description": "客户端版本号，默认为空", "title": "客户端版本号，默认为空"}, "device_id": {"type": "string", "default": "\"\"", "description": "设备id，默认为空", "title": "设备id，默认为空"}, "platform": {"type": "string", "default": "\"\"", "description": "平台(qq,wx,andrioid,ios)，默认为空", "title": "平台(qq,wx,andrioid,ios)，默认为空"}, "lang": {"type": "string", "default": "\"\"", "description": "客户端语言，默认为空", "title": "客户端语言，默认为空"}}, "description": "通用查询信息", "title": "通用查询信息", "required": ["uid", "token"]}, "msgcommonRespCommon": {"type": "object", "properties": {"ret": {"type": "integer", "format": "int32", "description": "返回值，0表示成功，其他为错误码", "title": "返回值，0表示成功，其他为错误码"}, "msg": {"type": "string", "description": "成功返回\"ok\"，其他为错误消息", "title": "成功返回\"ok\"，其他为错误消息"}, "request_id": {"type": "string", "description": "请求id", "title": "请求id"}}, "description": "通用返回信息", "title": "通用返回信息", "required": ["ret", "msg", "request_id"]}, "orderprotoAttachment": {"type": "object", "properties": {"name": {"type": "string"}, "path": {"type": "string"}}, "description": "附件", "title": "附件"}, "orderprotoBuyCloudMysqlReq": {"type": "object", "properties": {"dry_run": {"type": "boolean", "format": "boolean", "description": "是否测试", "title": "是否测试"}, "ops_audit_email": {"type": "string", "description": "运维审批人", "title": "运维审批人"}, "apply_msg": {"type": "string", "description": "申请理由", "title": "申请理由"}, "cost_model_node_id": {"type": "integer", "format": "int32", "description": "挂载节点id", "title": "挂载节点id"}, "supplier": {"type": "string", "description": "供应山", "title": "供应山"}, "region_id": {"type": "string", "description": "地区", "title": "地区"}, "zone_id": {"type": "string", "description": "可用区", "title": "可用区"}, "instance_type_id": {"type": "string", "description": "实例规格", "title": "实例规格"}, "instance_name": {"type": "string", "description": "实例名称", "title": "实例名称"}, "volume": {"$ref": "#/definitions/orderprotoMysqlVolume", "description": "硬盘", "title": "硬盘"}, "version": {"type": "string", "description": "版本", "title": "版本"}, "replica_num": {"type": "integer", "format": "int32", "description": "副本数", "title": "副本数"}, "backup_one_zone_id": {"type": "string", "description": "副本可用区1", "title": "副本可用区1"}, "backup_two_zone_id": {"type": "string", "description": "副本可用区2", "title": "副本可用区2"}, "ignore_case": {"type": "boolean", "format": "boolean", "description": "是否大小写铭感", "title": "是否大小写铭感"}, "instance_type": {"$ref": "#/definitions/orderprotoMysqlInstanceType", "description": "规格", "title": "规格"}, "proposer_email": {"type": "string"}}, "description": "云 Mysql 购买工单", "title": "云 Mysql 购买工单", "required": ["dry_run", "ops_audit_email", "apply_msg", "cost_model_node_id", "supplier", "region_id", "zone_id", "instance_type_id", "instance_name", "volume", "version", "replica_num", "backup_one_zone_id", "backup_two_zone_id"]}, "orderprotoBuyCloudMysqlResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoBuyCloudRedisReq": {"type": "object", "properties": {"dry_run": {"type": "boolean", "format": "boolean", "description": "是否测试", "title": "是否测试"}, "ops_audit_email": {"type": "string", "description": "运维审批人", "title": "运维审批人"}, "proposer_email": {"type": "string", "description": "申请人邮箱", "title": "申请人邮箱"}, "apply_msg": {"type": "string", "description": "申请理由", "title": "申请理由"}, "cost_model_node_id": {"type": "integer", "format": "int32", "description": "挂载节点id", "title": "挂载节点id"}, "supplier": {"type": "string", "description": "供应山", "title": "供应山"}, "region_id": {"type": "string", "description": "地区", "title": "地区"}, "zone_id": {"type": "string", "description": "可用区", "title": "可用区"}, "instance_type_id": {"type": "string", "description": "实例规格", "title": "实例规格"}, "instance_name": {"type": "string", "description": "实例名称", "title": "实例名称"}, "version": {"type": "string", "description": "版本", "title": "版本"}, "replica_num": {"type": "integer", "format": "int32", "description": "副本数", "title": "副本数"}, "backup_zone_id": {"type": "string", "description": "副本可用区", "title": "副本可用区"}, "shard_num": {"type": "integer", "format": "int32", "description": "切片数量", "title": "切片数量"}, "memory_gb": {"type": "number", "format": "float", "description": "内存", "title": "内存"}}, "description": "云Redis购买工单", "title": "云Redis购买工单", "required": ["dry_run", "ops_audit_email", "apply_msg", "cost_model_node_id", "supplier", "region_id", "zone_id", "instance_type_id", "instance_name", "version", "replica_num", "backup_zone_id"]}, "orderprotoBuyCloudRedisResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoBuyCloudServerReq": {"type": "object", "properties": {"supplier": {"type": "string", "description": "地区", "title": "地区"}, "region_id": {"type": "string", "description": "地区", "title": "地区"}, "zone_id": {"type": "string", "description": "可用区", "title": "可用区"}, "instance_type_id": {"type": "string", "description": "实例规格", "title": "实例规格"}, "sys_disk": {"$ref": "#/definitions/orderprotoDisk", "description": "系统盘", "title": "系统盘"}, "data_disk": {"type": "array", "items": {"$ref": "#/definitions/orderprotoDisk"}, "description": "数据盘", "title": "数据盘"}, "image_id": {"type": "string", "description": "数据盘镜像", "title": "数据盘镜像"}, "instance_name": {"type": "string", "description": "实例名称", "title": "实例名称"}, "password": {"type": "string", "description": "密码", "title": "密码"}, "dry_run": {"type": "boolean", "format": "boolean", "description": "是否测试", "title": "是否测试"}, "apply_msg": {"type": "string", "description": "申请理由", "title": "申请理由"}, "cost_model_node_id": {"type": "integer", "format": "int32", "description": "挂载节点id", "title": "挂载节点id"}, "ops_audit_email": {"type": "string", "description": "运维审批人", "title": "运维审批人"}, "proposer_email": {"type": "string"}}, "description": "云服务器购买工单", "title": "云服务器购买工单", "required": ["supplier", "region_id", "zone_id", "instance_type_id", "sys_disk", "data_disk", "image_id", "instance_name", "password", "dry_run", "apply_msg", "cost_model_node_id", "ops_audit_email"]}, "orderprotoBuyCloudServerResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoCcUserInfo": {"type": "object", "properties": {"cc_open_id": {"type": "string"}, "cc_email": {"type": "string"}}, "description": "抄送用户信息", "title": "抄送用户信息"}, "orderprotoCdnDomainCreateReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "domain_name": {"type": "string", "description": "加速域名", "title": "加速域名"}, "business_type": {"type": "string", "description": "域名业务类型 若为web，则表示类型为网页加速；若为download，则表示业务类型为文件下载加速；若为video，则表示业务类型为点播加速；若为wholeSite，则表示业务类型为全站加速", "title": "域名业务类型 若为web，则表示类型为网页加速；若为download，则表示业务类型为文件下载加速；若为video，则表示业务类型为点播加速；若为wholeSi"}, "ip_or_domain": {"type": "string", "description": "源站域名或源站IP", "title": "源站域名或源站IP"}, "domain_service_area": {"type": "string", "description": "域名服务范围 mainland_china，则表示服务范围为中国大陆；若为outside_mainland_china，则表示服务范围为中国大陆境外；若为global，则表示服务范围为全球", "title": "域名服务范围 mainland_china，则表示服务范围为中国大陆；若为outside_mainland_china，则表示服务范围为中国大陆境外；若为global，则表示服务范围为��"}, "origin_type": {"type": "string", "description": "源站类型取值：ipaddr、 domain、obs_bucket，分别表示：源站IP、源站域名、OBS桶访问域名", "title": "源站类型取值：ipaddr、 domain、obs_bucket，分别表示：源站IP、源站域名、OBS桶访问域名"}, "ops_audit_email": {"type": "string", "description": "运维审批人", "title": "运维审批人"}, "apply_msg": {"type": "string", "description": "申请理由", "title": "申请理由"}}, "description": "华为cdn加速域名创建", "title": "华为cdn加速域名创建", "required": ["common", "domain_name", "business_type", "ip_or_domain", "domain_service_area", "origin_type", "ops_audit_email", "apply_msg"]}, "orderprotoCdnDomainCreateResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "result": {"type": "integer", "format": "int32", "description": "返回内容", "title": "返回内容"}}, "required": ["resp_common", "result"]}, "orderprotoCommonOrderReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "order_type": {"type": "string", "description": "工单类型", "title": "工单类型"}, "apply_info": {"type": "string", "description": "表单json数据", "title": "表单json数据"}, "apply_msg": {"type": "string", "description": "表单json数据", "title": "表单json数据"}, "attachment_info": {"type": "array", "items": {"$ref": "#/definitions/orderprotoAttachment"}, "description": "表单json数据", "title": "表单json数据"}, "exigency": {"type": "integer", "format": "int32", "description": "是否加急，0为否，1为是", "title": "是否加急，0为否，1为是"}, "title": {"type": "string", "description": "标题", "title": "标题"}, "cc_user_infos": {"type": "array", "items": {"$ref": "#/definitions/orderprotoCcUserInfo"}, "description": "抄送人信息列表", "title": "抄送人信息列表"}}, "description": "通用工单", "title": "通用工单", "required": ["common", "order_type", "apply_info", "apply_msg", "attachment_info", "exigency", "title"]}, "orderprotoCommonOrderResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "result": {"type": "integer", "format": "int32", "description": "返回内容", "title": "返回内容"}}, "required": ["resp_common", "result"]}, "orderprotoCostModelApportionConfReq": {"type": "object", "properties": {"apply_msg": {"type": "string", "description": "申请信息", "title": "申请信息"}, "path_list": {"type": "array", "items": {"$ref": "#/definitions/orderprotoModelNodePath"}, "description": "路径信息", "title": "路径信息"}}, "description": "成本模型分摊设置工单", "title": "成本模型分摊设置工单", "required": ["apply_msg", "path_list"]}, "orderprotoCostModelApportionConfResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoDeleteModelNodeReq": {"type": "object", "properties": {"apply_msg": {"type": "string", "description": "申请信息", "title": "申请信息"}, "node_id": {"type": "integer", "format": "int32", "description": "节点层级", "title": "节点层级"}, "audit_owner": {"type": "string", "description": "审批负责人", "title": "审批负责人"}}, "description": "模型节点删除工单", "title": "模型节点删除工单", "required": ["apply_msg", "node_id"]}, "orderprotoDeleteModelNodeResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoDisk": {"type": "object", "properties": {"size_gb": {"type": "integer", "format": "int32"}, "category": {"type": "string"}}}, "orderprotoDomainOrderReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "domain": {"type": "string", "description": "域名", "title": "域名"}, "whois": {"type": "string", "description": "域名持有人", "title": "域名持有人"}, "describe": {"type": "string", "description": "描述", "title": "描述"}, "buy_time": {"type": "integer", "format": "int32", "description": "购买时常", "title": "购买时常"}, "business_tree_branch": {"type": "string", "description": "业务树", "title": "业务树"}, "ops_owner": {"type": "string", "description": "运维负责人", "title": "运维负责人"}}, "description": "购买域名工单", "title": "购买域名工单", "required": ["common", "domain", "describe", "buy_time", "business_tree_branch"]}, "orderprotoDomainOrderResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "result": {"type": "integer", "format": "int32", "description": "返回内容", "title": "返回内容"}}, "required": ["resp_common", "result"]}, "orderprotoDomainResolveReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "domain": {"type": "string", "description": "域名", "title": "域名"}, "record_type": {"type": "string", "description": "域名记录类型，大写英文，比如：A CNAME MX", "title": "域名记录类型，大写英文，比如：A CNAME MX"}, "value": {"type": "string", "description": "记录值，如 IP : ***************， CNAME : cname.dnspod.com.， MX : mail.dnspod.com", "title": "记录值，如 IP : ***************， CNAME : cname.dnspod.com.， MX : mail.dnspod.com"}, "ops_audit_email": {"type": "string", "description": "运维审批人", "title": "运维审批人"}, "apply_msg": {"type": "string", "description": "申请理由", "title": "申请理由"}}, "description": "腾讯云域名解析", "title": "腾讯云域名解析", "required": ["common", "domain", "record_type", "value", "ops_audit_email", "apply_msg"]}, "orderprotoDomainResolveResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "result": {"type": "integer", "format": "int32", "description": "返回内容", "title": "返回内容"}}, "required": ["resp_common", "result"]}, "orderprotoDragModelNodeReq": {"type": "object", "properties": {"apply_msg": {"type": "string", "description": "申请信息", "title": "申请信息"}, "node_id": {"type": "integer", "format": "int32", "description": "旧节点ID", "title": "旧节点ID"}, "new_parent_node_id": {"type": "integer", "format": "int32", "description": "新父节点ID", "title": "新父节点ID"}, "audit_owner": {"type": "string", "description": "审批负责人", "title": "审批负责人"}}, "description": "模型节点移动工单", "title": "模型节点移动工单", "required": ["apply_msg", "node_id", "new_parent_node_id"]}, "orderprotoDragModelNodeResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoGetMyAuditOrderReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon", "description": "通用头", "title": "通用头"}, "page": {"type": "string", "format": "int64"}, "page_size": {"type": "string", "format": "int64"}, "order_id": {"type": "string", "description": "工单号（模糊搜索）", "title": "工单号（模糊搜索）"}, "title": {"type": "string", "description": "工单标题（模糊搜索）", "title": "工单标题（模糊搜索）"}, "applied_start_date": {"type": "string", "description": "申请开始时间（ISO 8601格式）", "title": "申请开始时间（ISO 8601格式）"}, "applied_end_date": {"type": "string", "description": "申请结束时间（ISO 8601格式）", "title": "申请结束时间（ISO 8601格式）"}, "order_types": {"type": "array", "items": {"type": "string"}, "description": "工单类型数组", "title": "工单类型数组"}, "filter_by_role": {"type": "array", "items": {"type": "string"}, "description": "迭代三再实现：按角色筛选", "title": "迭代三再实现：按角色筛选"}}, "description": "获取我的审批工单", "title": "获取我的审批工单", "required": ["common"]}, "orderprotoGetMyAuditOrderResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "total": {"type": "integer", "format": "int32"}, "orders": {"type": "array", "items": {"$ref": "#/definitions/orderprotoOrder"}}, "order_types": {"type": "array", "items": {"type": "string"}, "description": "用户拥有的所有工单类型", "title": "用户拥有的所有工单类型"}}, "required": ["resp_common"]}, "orderprotoGetMyDoingOrderReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon", "description": "通用头", "title": "通用头"}, "page": {"type": "string", "format": "int64"}, "page_size": {"type": "string", "format": "int64"}, "filter_by_role": {"type": "array", "items": {"type": "string"}, "description": "迭代三再实现：按角色筛选", "title": "迭代三再实现：按角色筛选"}}, "description": "获取进行中的工单", "title": "获取进行中的工单", "required": ["common"]}, "orderprotoGetMyDoingOrderResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "total": {"type": "integer", "format": "int32"}, "orders": {"type": "array", "items": {"$ref": "#/definitions/orderprotoOrder"}}}, "required": ["resp_common"]}, "orderprotoGetMyDoneOrderReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon", "description": "通用头", "title": "通用头"}, "page": {"type": "string", "format": "int64"}, "page_size": {"type": "string", "format": "int64"}, "filter_by_role": {"type": "array", "items": {"type": "string"}, "description": "迭代三再实现：按角色筛选", "title": "迭代三再实现：按角色筛选"}}, "description": "获取已完结工单", "title": "获取已完结工单", "required": ["common"]}, "orderprotoGetMyDoneOrderResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "total": {"type": "integer", "format": "int32"}, "orders": {"type": "array", "items": {"$ref": "#/definitions/orderprotoOrder"}}}, "required": ["resp_common"]}, "orderprotoGetOrderDetailReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon", "description": "通用头", "title": "通用头"}, "order_id": {"type": "string"}}, "description": "获取工单详情", "title": "获取工单详情", "required": ["common"]}, "orderprotoGetOrderDetailResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "order_info": {"$ref": "#/definitions/orderprotoOrder"}, "stage_infos": {"type": "array", "items": {"$ref": "#/definitions/orderprotoStage"}}}, "required": ["resp_common"]}, "orderprotoKsServerApplyReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon", "description": "通用头", "title": "通用头"}, "region": {"type": "string"}, "zone": {"type": "string"}, "image": {"type": "string"}, "hardware_spec": {"type": "string", "description": "硬件规格ID", "title": "硬件规格ID"}, "hardware_spec_detail": {"type": "string", "description": "硬件规格描述", "title": "硬件规格描述"}, "sys_disk_type": {"type": "string"}, "data_disk_type": {"type": "string"}, "data_disk_size": {"type": "string", "format": "int64"}, "apply_msg": {"type": "string", "description": "申请理由", "title": "申请理由"}, "charge_type": {"type": "string", "description": "收费模式", "title": "收费模式"}, "instance_name": {"type": "string"}, "business_tree_branch": {"type": "string"}, "proposer_email": {"type": "string"}, "ops_owner_email": {"type": "string"}, "exigency": {"type": "integer", "format": "int32", "description": "是否加急，0为否，1为是 ", "title": "是否加急，0为否，1为是 "}}, "description": "金三云服务器申请", "title": "金三云服务器申请", "required": ["common", "hardware_spec", "hardware_spec_detail", "charge_type", "exigency"]}, "orderprotoKsServerApplyResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "order_id": {"type": "string"}}, "required": ["resp_common"]}, "orderprotoModelNodePath": {"type": "object", "properties": {"parent_id": {"type": "integer", "format": "int32"}, "child_id": {"type": "integer", "format": "int32"}, "weight": {"type": "integer", "format": "int32"}, "auto_weight": {"type": "integer", "format": "int32"}}}, "orderprotoModifyModelNodeReq": {"type": "object", "properties": {"apply_msg": {"type": "string", "description": "申请信息", "title": "申请信息"}, "node_id": {"type": "integer", "format": "int32", "description": "节点层级", "title": "节点层级"}, "node_name": {"type": "string", "description": "节点名称", "title": "节点名称"}, "business_owner": {"type": "string", "description": "业务负责人", "title": "业务负责人"}, "product_owner": {"type": "string", "description": "产品负责人", "title": "产品负责人"}, "develop_owner": {"type": "string", "description": "开发负责人", "title": "开发负责人"}, "audit_owner": {"type": "string", "description": "审批负责人", "title": "审批负责人"}}, "description": "模型节点修改工单", "title": "模型节点修改工单", "required": ["apply_msg", "node_id", "node_name", "business_owner", "product_owner", "develop_owner"]}, "orderprotoModifyModelNodeResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoMysqlInstanceType": {"type": "object", "properties": {"instance_type_group": {"type": "string"}, "Instance_type_id": {"type": "string"}, "cpu": {"type": "integer", "format": "int32"}, "memory_gb": {"type": "integer", "format": "int32"}}}, "orderprotoMysqlVolume": {"type": "object", "properties": {"category": {"type": "string"}, "size_gb": {"type": "integer", "format": "int32"}}}, "orderprotoNewModelChildNodeReq": {"type": "object", "properties": {"apply_msg": {"type": "string", "description": "申请信息", "title": "申请信息"}, "parent_id": {"type": "integer", "format": "int32", "description": "父节点id", "title": "父节点id"}, "node_name": {"type": "string", "description": "节点名称", "title": "节点名称"}, "product_owner": {"type": "string", "description": "产品负责人", "title": "产品负责人"}, "develop_owner": {"type": "string", "description": "开发负责人", "title": "开发负责人"}, "audit_owner": {"type": "string", "description": "审批负责人", "title": "审批负责人"}}, "description": "模型子节点新增工单", "title": "模型子节点新增工单", "required": ["apply_msg", "parent_id", "node_name"]}, "orderprotoNewModelChildNodeResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoNewModelNodeReq": {"type": "object", "properties": {"apply_msg": {"type": "string", "description": "申请信息", "title": "申请信息"}, "node_layer": {"type": "integer", "format": "int32", "description": "节点层级", "title": "节点层级"}, "node_name": {"type": "string", "description": "节点名称", "title": "节点名称"}, "business_owner": {"type": "string", "description": "业务负责人", "title": "业务负责人"}, "audit_owner": {"type": "string", "description": "审批负责人", "title": "审批负责人"}}, "description": "模型节点新增工单", "title": "模型节点新增工单", "required": ["apply_msg", "node_layer", "node_name", "business_owner"]}, "orderprotoNewModelNodeResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoOrder": {"type": "object", "properties": {"order_id": {"type": "string"}, "order_type": {"type": "string"}, "order_type_name": {"type": "string"}, "total_stage_num": {"type": "integer", "format": "int32"}, "current_stage": {"type": "integer", "format": "int32"}, "current_stage_name": {"type": "string"}, "stage_operator": {"type": "string"}, "proposer_email": {"type": "string"}, "ops_owner_email": {"type": "string"}, "apply_msg": {"type": "string"}, "info": {"type": "string"}, "result": {"type": "integer", "format": "int32"}, "result_desc": {"type": "string"}, "result_msg": {"type": "string"}, "apply_datetime": {"type": "string"}, "last_updated_datetime": {"type": "string"}, "is_cc": {"type": "integer", "format": "int32"}, "approval_status_for_me": {"type": "integer", "format": "int32"}}}, "orderprotoOrderApprovalReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "order_id": {"type": "string", "description": "工单ID", "title": "工单ID"}, "stage_num": {"type": "integer", "format": "int32", "description": "工单阶段数", "title": "工单阶段数"}, "chosen_action": {"type": "string", "description": "审批结果，e", "title": "审批结果，e"}}, "description": "页面审批", "title": "页面审批", "required": ["common", "order_id", "stage_num", "chosen_action"]}, "orderprotoOrderApprovalResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoResouceDeleteReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "supplier": {"type": "integer", "format": "int32"}, "region": {"type": "string"}, "resource_type": {"type": "string"}, "resource_id": {"type": "string"}, "resource_name": {"type": "string"}, "apply_msg": {"type": "string"}, "ops_audit_email": {"type": "string"}}, "description": "云资源销毁", "title": "云资源销毁", "required": ["common", "supplier", "region", "resource_type", "resource_id", "resource_name", "apply_msg", "ops_audit_email"]}, "orderprotoResouceDeleteResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon"}, "result": {"type": "integer", "format": "int32", "description": "返回内容", "title": "返回内容"}}, "required": ["resp_common", "result"]}, "orderprotoServerJumpImpowerReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "server_ip": {"type": "array", "items": {"type": "string"}, "description": "ip地址", "title": "ip地址"}, "impower_type": {"type": "integer", "format": "int32", "description": "权限类型 1 为普通权限，2 为root权限", "title": "权限类型 1 为普通权限，2 为root权限"}, "day_num": {"type": "integer", "format": "int32", "description": "权限持续时长，单位：天", "title": "权限持续时长，单位：天"}, "apply_msg": {"type": "string", "description": "申请理由", "title": "申请理由"}, "exigency": {"type": "integer", "format": "int32", "description": "是否加急，0为否，1为是 ", "title": "是否加急，0为否，1为是 "}}, "description": "服务器跳板机账号授权", "title": "服务器跳板机账号授权", "required": ["common", "server_ip", "impower_type", "day_num", "exigency"]}, "orderprotoServerJumpImpowerResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "result": {"type": "integer", "format": "int32", "description": "返回内容", "title": "返回内容"}}, "required": ["resp_common", "result"]}, "orderprotoStage": {"type": "object", "properties": {"stage_num": {"type": "integer", "format": "int32"}, "stage_name": {"type": "string"}, "stage_type": {"type": "string"}, "stage_operator": {"type": "string"}, "stage_result": {"type": "integer", "format": "int32"}, "stage_result_desc": {"type": "string"}, "stage_result_msg": {"type": "string"}, "apply_datetime": {"type": "string"}, "last_updated_datetime": {"type": "string"}}}, "orderprotoTokenApplyReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "cluster": {"type": "string"}, "token_type": {"type": "string"}, "ns": {"type": "string"}, "name": {"type": "string"}, "cost_model_node_id": {"type": "integer", "format": "int32", "description": "挂载节点id", "title": "挂载节点id"}, "ops_audit_email": {"type": "string"}, "apply_msg": {"type": "string"}, "path": {"type": "string"}, "proposer_email": {"type": "string"}}, "description": "k8s token 申请", "title": "k8s token 申请", "required": ["common", "cluster", "token_type", "ns", "name", "cost_model_node_id", "ops_audit_email", "apply_msg", "path", "proposer_email"]}, "orderprotoTokenApplyResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon"}, "result": {"type": "integer", "format": "int32", "description": "返回内容", "title": "返回内容"}}, "required": ["resp_common", "result"]}, "orderprotoTurnFlowAuditReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "order_id": {"type": "string", "description": "工单ID", "title": "工单ID"}, "stage_num": {"type": "integer", "format": "int32", "description": "工单阶段数", "title": "工单阶段数"}, "new_operator_email": {"type": "string", "description": "审批结果，e", "title": "审批结果，e"}}, "description": "审批人变更", "title": "审批人变更", "required": ["common", "order_id", "stage_num", "new_operator_email"]}, "orderprotoTurnFlowAuditResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoUpdateCMDBResourceServiceOwnerReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon"}, "new_service_owner": {"type": "string"}, "old_service_owner": {"type": "string"}, "ids": {"type": "array", "items": {"type": "string"}}, "apply_msg": {"type": "string"}, "old_service_owner_leader_audit_email": {"type": "string"}, "new_service_owner_leader_audit_email": {"type": "string"}, "ops_audit_email": {"type": "string"}}, "description": "更新资源服务负责人", "title": "更新资源服务负责人", "required": ["common"]}, "orderprotoUpdateCMDBResourceServiceOwnerResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}}, "required": ["resp_common"]}, "orderprotoValidateUserReq": {"type": "object", "properties": {"common": {"$ref": "#/definitions/msgcommonCommon", "description": "通用头", "title": "通用头"}, "email": {"type": "string"}}, "description": "用户校验请求", "title": "用户校验请求", "required": ["common"]}, "orderprotoValidateUserResp": {"type": "object", "properties": {"resp_common": {"$ref": "#/definitions/msgcommonRespCommon", "description": "通用返回头", "title": "通用返回头"}, "name": {"type": "string"}, "department": {"type": "string"}, "open_id": {"type": "string"}}, "description": "用户校验响应", "title": "用户校验响应", "required": ["resp_common"]}}}