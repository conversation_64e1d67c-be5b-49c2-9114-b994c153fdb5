package order

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"strings"

	"aix.zhhainiao.com/aixpublic/server/api/msgcommon"
	"aix.zhhainiao.com/aixpublic/server/core/ctxlog"
	"aix.zhhainiao.com/aixpublic/server/core/ctxutil"
	"aix.zhhainiao.com/aixpublic/utils/code"
	"aix.zhhainiao.com/app/ops/bpm/core/errors"
	"aix.zhhainiao.com/app/ops/bpm/plugins/cmdb/cmdbbiz"
	"aix.zhhainiao.com/app/ops/bpm/plugins/feishuhook/feishuhookdto"
	"aix.zhhainiao.com/app/ops/bpm/plugins/feishuservice/feishuservicedto"
	"aix.zhhainiao.com/app/ops/bpm/plugins/jumpserver/jumpserverbiz"
	"aix.zhhainiao.com/app/ops/bpm/plugins/jumpserver/jumpserverdto"
	"aix.zhhainiao.com/app/ops/bpm/plugins/jumpserver/jumpserverutil"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/config"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/hook"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/orderbiz"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/orderdto"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/orderproto"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/orderutil"
)

// KsServerApply 提交服务器购买工单
func (ctrl *OrderController) KsServerApply(ctx context.Context,
	req *orderproto.KsServerApplyReq) (resp *orderproto.KsServerApplyResp, err error) {
	resp = new(orderproto.KsServerApplyResp)

	orderInfo := orderdto.KsServerApplyOrderInfo{
		Region:             req.Region,
		Zone:               req.Zone,
		Image:              req.Image,
		HardwareSpec:       req.HardwareSpec,
		HardwareSpecDetail: req.HardwareSpecDetail,
		SysDiskType:        req.SysDiskType,
		DataDiskType:       req.DataDiskType,
		DataDiskSize:       req.DataDiskSize,
		InstanceName:       req.InstanceName,
		BusinessTreeBranch: req.BusinessTreeBranch,
		ChargeType:         req.ChargeType,
	}
	orderInfoByte, err := json.Marshal(orderInfo)
	if err != nil {
		return
	}

	// order := &orderdto.Order{
	// 	OrderType:     config.KsServerApplyOrderTypeConf.ID,
	// 	Info:          string(orderInfoByte),
	// 	Exigency:      int64(req.Exigency),
	// 	ApplyMsg:      req.ApplyMsg,
	// 	ProposerEmail: req.ProposerEmail,
	// 	OpsOwnerEmail: req.OpsOwnerEmail,
	// }
	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("KsServerApply KsServerApply err=%v", err)
	// 	return
	// }
	// go ctrl.Biz.HandleOrder(orderID)
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.KsServerApplyOrderTypeConf.ID,
		Info:          string(orderInfoByte),
		Exigency:      int64(req.Exigency),
		ApplyMsg:      req.ApplyMsg,
		ProposerEmail: req.ProposerEmail,
		OpsOwnerEmail: req.OpsOwnerEmail,
		CTime:         time.Now(),
	}

	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}

// GetMyDoneOrder 获取已完结工单
func (ctrl *OrderController) GetMyDoneOrder(ctx context.Context, req *orderproto.GetMyDoneOrderReq) (resp *orderproto.GetMyDoneOrderResp, err error) {
	resp = new(orderproto.GetMyDoneOrderResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)

	// 构建参数结构体
	param := &orderdto.MyDoneOrderSearchParam{
		PageNum:      int(req.Page),
		PageSize:     int(req.PageSize),
		UserEmail:    userEmail,
		FilterByRole: req.FilterByRole,
	}

	totalNum, orderInfos, err := ctrl.Biz.GetDoneOrdersByPage(ctx, param)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("MyDoneOrder Biz.GetDoneOrdersByPage err=%v", err)
	}
	for _, item := range orderInfos {
		resp.Orders = append(resp.Orders, &orderproto.Order{
			OrderId:             item.OrderID,
			OrderType:           item.OrderType,
			OrderTypeName:       item.OrderTypeName,
			TotalStageNum:       int32(item.TotalStageNum),
			CurrentStage:        int32(item.CurrentStage),
			ProposerEmail:       item.ProposerEmail,
			OpsOwnerEmail:       item.OpsOwnerEmail,
			Result:              int32(item.Result),
			ResultDesc:          orderdto.OrderResults[item.Result],
			ApplyDatetime:       item.ApplyDatetime,
			LastUpdatedDatetime: item.LastUpdatedDatetime,
			IsCc:                int32(item.IsCc),
			ApprovalStatusForMe: int32(item.ApprovalStatusForMe),
		})
	}
	resp.Total = int32(totalNum)
	return
}

// GetMyDoingOrder 获取正在进行的工单
func (ctrl *OrderController) GetMyDoingOrder(ctx context.Context, req *orderproto.GetMyDoingOrderReq) (resp *orderproto.GetMyDoingOrderResp, err error) {
	resp = new(orderproto.GetMyDoingOrderResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)

	// 构建参数结构体
	param := &orderdto.MyDoingOrderSearchParam{
		PageNum:      int(req.Page),
		PageSize:     int(req.PageSize),
		UserEmail:    userEmail,
		FilterByRole: req.FilterByRole,
	}

	totalNum, orderInfos, err := ctrl.Biz.GetDoingOrdersByPage(ctx, param)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("MyDoingOrder Biz.GetDoingOrdersByPage err=%v", err)
	}
	for _, item := range orderInfos {
		resp.Orders = append(resp.Orders, &orderproto.Order{
			OrderId:             item.OrderID,
			OrderType:           item.OrderType,
			OrderTypeName:       item.OrderTypeName,
			TotalStageNum:       int32(item.TotalStageNum),
			CurrentStage:        int32(item.CurrentStage),
			CurrentStageName:    item.CurrentStageName,
			StageOperator:       item.CurrentOperator,
			ProposerEmail:       item.ProposerEmail,
			OpsOwnerEmail:       item.OpsOwnerEmail,
			Result:              int32(item.Result),
			ResultDesc:          orderdto.OrderResults[item.Result],
			ApplyDatetime:       item.ApplyDatetime,
			LastUpdatedDatetime: item.LastUpdatedDatetime,
			IsCc:                int32(item.IsCc),
			ApprovalStatusForMe: int32(item.ApprovalStatusForMe),
		})
	}
	resp.Total = int32(totalNum)
	return
}

// GetOrderDetail 获取工单详情
func (ctrl *OrderController) GetOrderDetail(ctx context.Context, req *orderproto.GetOrderDetailReq) (resp *orderproto.GetOrderDetailResp, err error) {
	resp = new(orderproto.GetOrderDetailResp)
	orderInfo, stageInfos, err := ctrl.Biz.GetOrderDetail(ctx, req.OrderId)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetOrderDetail Biz.GetOrderDetail err=%v", err)
	}
	resp.OrderInfo = &orderproto.Order{
		OrderId:             orderInfo.OrderID,
		OrderType:           orderInfo.OrderType,
		OrderTypeName:       orderInfo.OrderTypeName,
		TotalStageNum:       int32(orderInfo.TotalStageNum),
		CurrentStage:        int32(orderInfo.CurrentStage),
		CurrentStageName:    orderInfo.CurrentStageName,
		ProposerEmail:       orderInfo.ProposerEmail,
		OpsOwnerEmail:       orderInfo.OpsOwnerEmail,
		ApplyMsg:            orderInfo.ApplyMsg,
		Info:                orderInfo.Info,
		Result:              int32(orderInfo.Result),
		ResultDesc:          orderInfo.ResultDesc,
		ResultMsg:           orderInfo.ResultMsg,
		ApplyDatetime:       orderInfo.ApplyDatetime,
		LastUpdatedDatetime: orderInfo.LastUpdatedDatetime,
	}
	for _, item := range stageInfos {
		resp.StageInfos = append(resp.StageInfos, &orderproto.Stage{
			StageNum:            int32(item.StageNum),
			StageName:           item.StageName,
			StageType:           item.StageType,
			StageOperator:       item.StageOperator,
			StageResult:         int32(item.StageResult),
			StageResultDesc:     item.StageResultDesc,
			StageResultMsg:      item.StageResultMsg,
			ApplyDatetime:       item.ApplyDatetime,
			LastUpdatedDatetime: item.LastUpdatedDatetime,
		})
	}
	return
}

// ServerJumpImpower 服务器跳板机账号授权
func (ctrl *OrderController) ServerJumpImpower(ctx context.Context, req *orderproto.ServerJumpImpowerReq) (resp *orderproto.ServerJumpImpowerResp, err error) {
	resp = new(orderproto.ServerJumpImpowerResp)

	if req.ImpowerType == 2 && req.DayNum > 3 {
		resp.RespCommon = &msgcommon.RespCommon{}
		resp.RespCommon.Msg = "sudo permission cannot exceed 3 days"
		return resp, errors.ApplicationDayMoreThan3Day
	}

	jumpserverCmdbInfo := jumpserverbiz.GetInstance().GetInfoByCmdbIp(ctx, req.ServerIp)

	var ipMap map[string]struct{} = make(map[string]struct{})
	for _, info := range jumpserverCmdbInfo {
		ipMap[info.PrivateIp] = struct{}{}
		ipMap[info.PublicIp] = struct{}{}
	}

	if len(jumpserverCmdbInfo) != len(req.ServerIp) {
		var errorIp []string = make([]string, 0)
		for _, reqIp := range req.ServerIp {
			if _, has := ipMap[reqIp]; !has {
				errorIp = append(errorIp, reqIp)
			}
		}
		resp.RespCommon = &msgcommon.RespCommon{}
		resp.RespCommon.Msg = fmt.Sprintf("these servers [%v] no found,check or clear it", errorIp)
		return resp, errors.NoMachineMatch
	}

	var approveMap map[string]*jumpserverdto.JumpserverApproverInfo = make(map[string]*jumpserverdto.JumpserverApproverInfo)

	user := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	for _, info := range jumpserverCmdbInfo {
		var approve string
		if info.ServiceOwner == "" || user == info.ServiceOwner {
			if info.OpsOwner != "" {
				approve = info.OpsOwner
			} else {
				approve = jumpserverutil.GetConfig().ApproveEmail
			}
		} else {
			approve = info.ServiceOwner
		}
		if value, has := approveMap[approve]; has {
			value.Name = append(value.Name, info.Name)
			value.PrivateIp = append(value.PrivateIp, info.PrivateIp)
		} else {
			temp := jumpserverdto.JumpserverApproverInfo{
				Name:         []string{info.Name},
				PrivateIp:    []string{info.PrivateIp},
				ApproveOwner: approve,
			}
			approveMap[approve] = &temp
		}
	}

	auditServer := jumpserverutil.GetDynConfig().Server
	for _, approve := range approveMap {
		approveDay := req.DayNum
		// 轮询审批机器，如果审批的机器中含有审计服务器，则将天数设为1
		for _, audit := range auditServer {
			if _, has := ipMap[audit]; has {
				approveDay = 1
			}
		}

		connIpAndApplyMsg := "申请ip：" + strings.Join(approve.PrivateIp, ",") + "\n机器名：" + strings.Join(approve.Name, ",") + "\n理由：" + req.ApplyMsg
		if req.ImpowerType == 2 {
			connIpAndApplyMsg = fmt.Sprintf(connIpAndApplyMsg+"\n申请天数：%d", approveDay)
		}

		userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
		// orderInfo := orderdto.ServerJumpImpowerOrderInfo{
		// 	Name:         strings.Join(approve.Name, ","),
		// 	ServerIP:     approve.PrivateIp,
		// 	ServiceOwner: approve.ApproveOwner,
		// 	// ServiceOpsOwner: serviceOpsOwner,
		// 	ImpowerType:   int64(req.ImpowerType),
		// 	DayNum:        int64(req.DayNum),
		// 	ProposerEmail: userEmail,
		// }
		// orderInfoByte, _ := json.Marshal(orderInfo)
		// order := &orderdto.Order{
		// 	OrderType:     config.ServerJumpImpowerOrderTypeConf.ID,
		// 	Info:          string(orderInfoByte),
		// 	Exigency:      int64(req.Exigency),
		// 	ApplyMsg:      connIpAndApplyMsg,
		// 	ProposerEmail: userEmail,
		// }

		// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
		// if err != nil {
		// 	ctxlog.WithCtx(ctx).Errorf("ServerJumpImpower CreateOrder err=%v", err)
		// }
		// go ctrl.Biz.HandleOrder(orderID)

		// 创建工单ID
		h := md5.New()
		h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
		orderID := hex.EncodeToString(h.Sum(nil))[8:24]

		orderInfo := orderdto.ServerJumpImpowerOrderInfo{
			Name:          strings.Join(approve.Name, ","),
			ServerIP:      approve.PrivateIp,
			ServiceOwner:  approve.ApproveOwner,
			ImpowerType:   int64(req.ImpowerType),
			DayNum:        int64(req.DayNum),
			ProposerEmail: userEmail,
		}
		orderInfoByte, _ := json.Marshal(orderInfo)

		order := &orderdto.Order{
			OrderID:       orderID,
			OrderType:     config.ServerJumpImpowerOrderTypeConf.ID,
			Info:          string(orderInfoByte),
			Exigency:      int64(req.Exigency),
			ApplyMsg:      connIpAndApplyMsg,
			ProposerEmail: userEmail,
			CTime:         time.Now(),
		}

		err = ctrl.Biz.NewCreateOrder(ctx, order)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
			return
		}

		go func(order *orderdto.Order) {
			err = ctrl.Biz.NewCreateStage(order, nil)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
				return
			}
		}(order)
	}
	return
}

// CdnDomainCreate 华为cdn域名创建
func (ctrl *OrderController) CdnDomainCreate(ctx context.Context, req *orderproto.CdnDomainCreateReq) (resp *orderproto.CdnDomainCreateResp, err error) {
	resp = new(orderproto.CdnDomainCreateResp)

	req.ApplyMsg = fmt.Sprintf("域名:%s \n源站:%s, \n申请理由:%s", req.DomainName, req.IpOrDomain, req.ApplyMsg)
	applyInfo := map[string]string{
		"ops_audit_email":     req.OpsAuditEmail,
		"domain_name":         req.DomainName,
		"business_type":       req.BusinessType,
		"ip_or_domain":        req.IpOrDomain,
		"domain_service_area": req.DomainServiceArea,
		"origin_type":         req.OriginType,
	}
	orderInfoByte, err := json.Marshal(applyInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CdnDomainCreate Marshal err=%v", err)
	}
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	// order := &orderdto.Order{
	// 	OrderType:     config.CdnCreateOrderTypeConf.ID,
	// 	Info:          string(orderInfoByte),
	// 	ApplyMsg:      req.ApplyMsg,
	// 	ProposerEmail: userEmail,
	// 	OpsOwnerEmail: req.OpsAuditEmail,
	// }
	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("CdnDomainCreate CreateOrder err=%v", err)
	// 	return
	// }
	// go ctrl.Biz.HandleOrder(orderID)
	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.CdnCreateOrderTypeConf.ID,
		Info:          string(orderInfoByte),
		ApplyMsg:      req.ApplyMsg,
		ProposerEmail: userEmail,
		OpsOwnerEmail: req.OpsAuditEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}

// CommonOrder 通用工单接口（所有工单类型都可以走这个接口）
func (ctrl *OrderController) CommonOrder(ctx context.Context, req *orderproto.CommonOrderReq) (resp *orderproto.CommonOrderResp, err error) {
	resp = new(orderproto.CommonOrderResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	// 获取工单配置对象
	orderTypeConf, ok := config.OrderTypeConfMap[req.OrderType]
	if !ok {
		err = errors.IllegalOrderTypeErrorError
		return
	}
	// 执行表单参数校验钩子
	if argsVerigyHandle, ok := config.ArgsVerigyHandleMap[orderTypeConf.ArgsVerifyHook]; ok {
		err = argsVerigyHandle(ctx, req.ApplyInfo)
		if err != nil {
			return
		}
	} else {
		err = errors.IllegalOrderTypeArgsVerigyHookError
		return
	}
	// 执行请求处理钩子
	for _, requestHandleName := range orderTypeConf.RequestHandleHooks {
		if requestHandle, ok := config.RequestHandleMap[requestHandleName]; ok {
			err = requestHandle(ctx, req)
			if err != nil {
				return
			}
		} else {
			err = errors.IllegalOrderTypeArgsHandleHookError
			return
		}
	}

	// 执行工单分割处理钩子
	var orderFormJsonInfos []string
	if SeparateOrderHandle, ok := config.SeparateOrderHandleMap[orderTypeConf.SeparateOrderHook]; ok {
		orderFormJsonInfos, err = SeparateOrderHandle(ctx, req.ApplyInfo)
		if err != nil {
			return
		}
	} else {
		err = errors.IllegalOrderTypeSeparateOrderHandleError
		return
	}
	// 创建工单并启用协程处理工单流程
	for _, item := range orderFormJsonInfos {
		var info map[string]interface{}
		if err = json.Unmarshal([]byte(item), &info); err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder Unmarshal err=%v", err)
			return
		}

		opsOwnerEmail := ""
		if info["ops_audit_email"] != nil {
			opsOwnerEmail = info["ops_audit_email"].(string)
		}

		// 创建工单ID
		h := md5.New()
		h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
		orderID := hex.EncodeToString(h.Sum(nil))[8:24]

		order := &orderdto.Order{
			OrderID:       orderID,
			OrderType:     orderTypeConf.ID,
			Title:         req.Title,
			Info:          item,
			Exigency:      int64(req.Exigency),
			ApplyMsg:      req.ApplyMsg,
			ProposerEmail: userEmail,
			OpsOwnerEmail: opsOwnerEmail,
			CTime:         time.Now(),
		}
		err = ctrl.Biz.NewCreateOrder(ctx, order)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
			return
		}

		// 处理抄送逻辑
		var ccUserInfos []*orderdto.CcUserInfo
		if len(req.CcUserInfos) > 0 {
			// 转换protobuf结构为DTO结构
			ccUserInfos = make([]*orderdto.CcUserInfo, 0, len(req.CcUserInfos))
			for _, ccInfo := range req.CcUserInfos {
				ccUserInfos = append(ccUserInfos, &orderdto.CcUserInfo{
					CcOpenID: ccInfo.CcOpenId,
					CcEmail:  ccInfo.CcEmail,
				})
			}

			err = ctrl.Biz.HandleOrderCc(ctx, order.OrderID, ccUserInfos)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("CommonOrder HandleOrderCc err=%v", err)
				// 抄送失败不影响工单创建，只记录错误日志
			}
		}

		go func(order *orderdto.Order, ccUserInfos []*orderdto.CcUserInfo) {
			err = ctrl.Biz.NewCreateStage(order, ccUserInfos)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
				return
			}
		}(order, ccUserInfos)
	}
	return
}
func (ctrl *OrderController) GetMyAuditOrder(ctx context.Context, req *orderproto.GetMyAuditOrderReq) (resp *orderproto.GetMyAuditOrderResp, err error) {
	resp = new(orderproto.GetMyAuditOrderResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)

	// 构建搜索参数
	pageNum := int(req.Page)
	if pageNum <= 0 {
		pageNum = 1 // 页码从1开始
	}
	pageSize := int(req.PageSize)
	if pageSize <= 0 {
		pageSize = 10 // 默认每页显示10条
	}

	if pageSize > 100 {
		pageSize = 100 // 最多每页显示100条
	}

	searchParam := &orderdto.MyAuditOrderSearchParam{
		PageNum:       pageNum,
		PageSize:      pageSize,
		OperatorEmail: userEmail,
		FilterByRole:  req.FilterByRole,
	}

	searchParam.OrderId = req.OrderId
	searchParam.Title = req.Title
	searchParam.OrderTypes = req.OrderTypes
	if req.AppliedStartDate != "" {
		searchParam.AppliedStartDate = &req.AppliedStartDate
	}
	if req.AppliedEndDate != "" {
		searchParam.AppliedEndDate = &req.AppliedEndDate
	}

	// 使用新的动态查询方法
	totalNum, orderInfos, err := ctrl.Biz.GetMyAuditOrderWithSearch(ctx, searchParam)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyAuditOrder GetMyAuditOrderWithSearch err=%v", err)
		return
	}

	// 获取用户作为审批人的所有工单类型
	orderTypes, err := ctrl.Biz.GetAuditOrderTypesByOperator(ctx, userEmail)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyAuditOrder GetAuditOrderTypesByOperator err=%v", err)
		// 如果获取工单类型失败，不影响主要功能，继续执行
		orderTypes = []string{}
	}

	for _, item := range orderInfos {
		resp.Orders = append(resp.Orders, &orderproto.Order{
			OrderId:             item.OrderID,
			OrderType:           item.OrderType,
			OrderTypeName:       item.OrderTypeName,
			TotalStageNum:       int32(item.TotalStageNum),
			CurrentStage:        int32(item.CurrentStage),
			CurrentStageName:    item.CurrentStageName,
			StageOperator:       item.CurrentOperator,
			ProposerEmail:       item.ProposerEmail,
			OpsOwnerEmail:       item.OpsOwnerEmail,
			ApplyMsg:            item.ApplyMsg,
			Info:                item.Info,
			Result:              int32(item.Result),
			ResultDesc:          orderdto.OrderResults[item.Result],
			ApplyDatetime:       item.ApplyDatetime,
			LastUpdatedDatetime: item.LastUpdatedDatetime,
			IsCc:                int32(item.IsCc),
			ApprovalStatusForMe: int32(item.ApprovalStatusForMe),
		})
	}
	resp.OrderTypes = orderTypes
	resp.Total = int32(totalNum)
	return
}
func (ctrl *OrderController) DomainResolve(ctx context.Context, req *orderproto.DomainResolveReq) (resp *orderproto.DomainResolveResp, err error) {
	resp = new(orderproto.DomainResolveResp)
	req.ApplyMsg = fmt.Sprintf("域名:%s %s \n%s", req.Domain, req.Value, req.ApplyMsg)
	applyMsg := map[string]string{
		"ops_audit_email": req.OpsAuditEmail,
		"record_type":     req.RecordType,
		"domain":          req.Domain,
		"value":           req.Value,
	}
	orderInfoByte, err := json.Marshal(applyMsg)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("DomainResolve Marshal err=%v", err)
	}
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	// order := &orderdto.Order{
	// 	OrderType:     config.DomainResolveOrderTypeConf.ID,
	// 	Info:          string(orderInfoByte),
	// 	ApplyMsg:      req.ApplyMsg,
	// 	ProposerEmail: userEmail,
	// 	OpsOwnerEmail: req.OpsAuditEmail,
	// }

	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("DomainResolve CreateOrder err=%v", err)
	// 	return
	// }

	// go ctrl.Biz.HandleOrder(orderID)

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.DomainResolveOrderTypeConf.ID,
		Info:          string(orderInfoByte),
		ApplyMsg:      req.ApplyMsg,
		ProposerEmail: userEmail,
		OpsOwnerEmail: req.OpsAuditEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return

}

// OrderApproval 页面审批
func (ctrl *OrderController) OrderApproval(ctx context.Context, req *orderproto.OrderApprovalReq) (resp *orderproto.OrderApprovalResp, err error) {
	resp = new(orderproto.OrderApprovalResp)
	// userFeishuEmail := ctxutil.GetReqInfo(ctx).Get("feiShuEmail").(string)
	// // 获取数据库阶段记录，获取msg_id
	// auditInfo, err := ctrl.Biz.GetAuditStageInfoByMysql(ctx, req.OrderId, int64(req.StageNum))
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("OrderApproval GetAuditStageMsgID err = %v", err)
	// 	return
	// }
	// // 判断是否该阶段审批人
	// if userFeishuEmail != auditInfo.FeiShuUserEmail {
	// 	err = orderutil.StageAuditOperatorErr
	// 	return
	// }

	// // 修改redis 完成审批操作
	// callBackResult := &feishuhookdto.AuditMsgCardResult{
	// 	OpenMessageID: auditInfo.FeiShuMsgID,
	// 	OpenID:        auditInfo.FeiShuUserOpenID,
	// 	UserID:        auditInfo.FeiShuUserID,
	// 	ActionResult:  req.ChosenAction,
	// }
	// err = ctrl.FeishuHookBiz.HandleMsgCardCallBack(ctx, callBackResult)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("feishu callback HandleMsgCardCallBack err = %v", err)
	// }
	userFeishuEmail := ctxutil.GetReqInfo(ctx).Get("feiShuEmail").(string)

	currentStage, err := ctrl.Biz.Dao.StageMysql.GetStage(ctx, req.OrderId, int64(req.StageNum))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("OrderApproval GetAuditStageMsgID err = %v", err)
		return
	}
	user := strings.Split(userFeishuEmail, "@")[0]
	stageOperator := strings.Split(currentStage.Operator, "@")[0]
	if user != stageOperator {
		err = orderutil.StageAuditOperatorErr
		return
	}

	// 获取审批阶段信息
	tbStages, err := ctrl.Biz.Dao.StageMysql.GetStages(ctx, req.OrderId)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewSendFeiShuAudit GetStages err=%v", err)
		return
	}

	// 获取审批阶段信息
	tbOrder, err := ctrl.Biz.Dao.OrderMysql.GetOrder(ctx, req.OrderId)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewSendFeiShuAudit GetOrder err=%v", err)
		return
	}

	// 组装审批人员卡片
	var auditPeoples []string = make([]string, 0)
	var stageMessageID map[int]string = make(map[int]string)
	for _, stage := range tbStages {
		noEmailOperator := strings.ReplaceAll(stage.Operator, "@cmcm.com", "")
		auditPeoples = append(auditPeoples, noEmailOperator)
		stageMessageID[stage.StageNum] = stage.Info
	}

	// 获取不同工单的任务流配置信息
	orderTypeConf, ok := config.OrderTypeConfMap[tbOrder.OrderType]
	if !ok {
		ctxlog.WithCtx(ctx).Errorf("NewSendFeiShuAudit OrderTypeConfMap[%s] not value", tbOrder.OrderType)
		return
	}

	proposerInfo, err := ctrl.Biz.GetPersonInfo(ctx, tbOrder.ProposerEmail)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewSendFeiShuAudit GetPersonInfo err=%v", err)
		return
	}

	// 组装审批结果卡片
	_, auditStatusDetails, auditStatusColors := orderbiz.GetAuditStatus(ctx, tbStages, int(req.StageNum))

	auditResult := feishuhookdto.AGREE_EN
	if req.ChosenAction == feishuservicedto.AUDIT_NO {
		auditResult = feishuhookdto.REJECT_EN
	}

	var stageInfo orderdto.StageInfo
	err = json.Unmarshal([]byte(stageMessageID[int(req.StageNum)]), &stageInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewSendFeiShuAudit Unmarshal stageInfo err=%v", err)
		return
	}

	// 组装参数
	param := &feishuhookdto.AuditHookParam{
		OrderId:                req.OrderId,
		ProposerEmail:          proposerInfo.UserInfo.Email,
		ProposerUserId:         proposerInfo.UserInfo.UserID,
		ProposerDepartmentName: proposerInfo.DepartmentInfo.Name,
		OrderTypeNameCN:        orderTypeConf.Name,
		CurrentStage:           int(req.StageNum),
		AuditPepoles:           auditPeoples,
		AuditResultsCN:         auditStatusDetails,
		AuditResultsColorCN:    auditStatusColors,
		AuditResult:            auditResult,
		CurrentMessageId:       stageInfo.FeiShuMsgID,
	}
	err = ctrl.FeishuHookBiz.NewHandleMsgCardCallBack(param)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("OrderApproval NewHandleMsgCardCallBack err = %v", err)
	}
	return
}

// TurnFlowAudit 审批人流转
func (ctrl *OrderController) TurnFlowAudit(ctx context.Context, req *orderproto.TurnFlowAuditReq) (resp *orderproto.TurnFlowAuditResp, err error) {
	resp = new(orderproto.TurnFlowAuditResp)
	resp.RespCommon = &msgcommon.RespCommon{}

	stageAuditInfos, err := ctrl.Biz.TurnAuditOperator(ctx, req.OrderId, req.NewOperatorEmail, int64(req.StageNum))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("TurnFlowAudit TurnAuditOperator err = %v", err)
		if baseErr, ok := err.(code.BaseError); ok {
			resp.RespCommon.Ret = int32(baseErr.Ret)
			resp.RespCommon.Msg = baseErr.Error()
		}
		return
	}

	// 获取审批/执行情况消息卡片基础内容
	err = ctrl.Biz.SendMsgCardForTurnFlowAudit(ctx, req.OrderId, int64(req.StageNum),
		req.NewOperatorEmail, stageAuditInfos)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("TurnFlowAudit SendMsgCardForTurnFlowAudit err = %v", err)
		resp.RespCommon.Ret = int32(orderutil.TurnAuditOperatorFailErr.Ret)
		resp.RespCommon.Msg = orderutil.TurnAuditOperatorFailErr.Error()
		return
	}

	resp.RespCommon.Ret = 0
	resp.RespCommon.Msg = "变更审批人成功"
	return
}
func (ctrl *OrderController) NewModelNode(ctx context.Context, req *orderproto.NewModelNodeReq) (resp *orderproto.NewModelNodeResp, err error) {
	resp = new(orderproto.NewModelNodeResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	applyMsg := req.ApplyMsg
	req.ApplyMsg = ""
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.NewModelNodeOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}

	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}
func (ctrl *OrderController) CostModelApportionConf(ctx context.Context, req *orderproto.CostModelApportionConfReq) (resp *orderproto.CostModelApportionConfResp, err error) {
	resp = new(orderproto.CostModelApportionConfResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	applyMsg := req.ApplyMsg
	req.ApplyMsg = ""
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}
	// order := &orderdto.Order{
	// 	OrderType:     config.ConfCostModelApportionOrderTypeConf.ID,
	// 	Info:          string(orderInfoBytes),
	// 	ApplyMsg:      applyMsg,
	// 	ProposerEmail: userEmail,
	// }

	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("DomainResolve CreateOrder err=%v", err)
	// 	return
	// }

	// go ctrl.Biz.HandleOrder(orderID)

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.ConfCostModelApportionOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}
func (ctrl *OrderController) NewModelChildNode(ctx context.Context, req *orderproto.NewModelChildNodeReq) (resp *orderproto.NewModelChildNodeResp, err error) {
	resp = new(orderproto.NewModelChildNodeResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	applyMsg := req.ApplyMsg
	if req.DevelopOwner != "" {
		applyMsg = applyMsg + "\n" + fmt.Sprintf("节点开发负责人:%s", req.DevelopOwner)
	}
	if req.ProductOwner != "" {
		applyMsg = applyMsg + "\n" + fmt.Sprintf("节点产品负责人:%s", req.DevelopOwner)
	}
	req.ApplyMsg = ""
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}

	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.NewModelChildNodeOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewModelChildNode NewCreateOrder err=%v", err)
		return
	}

	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewModelChildNode NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("NewModelChildNode NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}
func (ctrl *OrderController) DomainOrder(ctx context.Context, req *orderproto.DomainOrderReq) (resp *orderproto.DomainOrderResp, err error) {
	resp = new(orderproto.DomainOrderResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)

	businessTrees := strings.Split(req.BusinessTreeBranch, ".")
	var businessTreesInt []int32 = make([]int32, 0, len(businessTrees))
	for _, businessTree := range businessTrees {
		temp, _ := strconv.ParseInt(businessTree, 10, 64)
		businessTreesInt = append(businessTreesInt, int32(temp))
	}

	businessTreeInfos, err := cmdbbiz.GetInstance().GetCmdbTreeModelNodeInfos(ctx, businessTreesInt)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("DomainOrder GetCmdbTreeModelNodeInfos req err=%v", err)
		return
	}
	var businessTreeInfosMap map[int32]string = make(map[int32]string)
	for _, businessTreeInfo := range businessTreeInfos {
		businessTreeInfosMap[businessTreeInfo.Id] = businessTreeInfo.Name
	}

	var businessTree []string = make([]string, 0)
	for _, businessTreeInt := range businessTreesInt {
		businessTree = append(businessTree, businessTreeInfosMap[businessTreeInt])
	}

	info := orderdto.DomainApplyInfo{
		Domain:             req.Domain,
		Whois:              req.Whois,
		Describe:           req.Describe,
		BuyTime:            req.BuyTime,
		BusinessTreeBranch: strings.Join(businessTree, "."),
		OpsAuditEmail:      req.OpsOwner,
	}

	orderInfoBytes, err := json.Marshal(info)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("DomainOrder Marshal req err=%v", err)
		return
	}

	msg := fmt.Sprintf("域名：%s\n域名持有者：%s\n购买时常：%v年\n业务负责人：%s\n业务树：%v\n用途：%s",
		req.Domain, req.Whois, req.BuyTime, userEmail, strings.Join(businessTree, "."), req.Describe)

	// order := &orderdto.Order{
	// 	OrderType:     config.ApplyDomainApplyOrderTypeConf.ID,
	// 	Info:          string(orderInfoBytes),
	// 	ApplyMsg:      msg,
	// 	ProposerEmail: userEmail,
	// 	OpsOwnerEmail: req.OpsOwner,
	// }

	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("DomainOrder CreateOrder err=%v", err)
	// 	return
	// }

	// go ctrl.Biz.HandleOrder(orderID)

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.ApplyDomainApplyOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      msg,
		ProposerEmail: userEmail,
		OpsOwnerEmail: req.OpsOwner,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)

	return
}

// 云服务器购买工单
func (ctrl *OrderController) BuyCloudServer(ctx context.Context, req *orderproto.BuyCloudServerReq) (resp *orderproto.BuyCloudServerResp,
	err error) {
	resp = new(orderproto.BuyCloudServerResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	req.ProposerEmail = userEmail
	applyMsg := req.ApplyMsg
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}
	// order := &orderdto.Order{
	// 	OrderType:     config.BuyCloudServerOrderTypeConf.ID,
	// 	Info:          string(orderInfoBytes),
	// 	ApplyMsg:      applyMsg,
	// 	ProposerEmail: userEmail,
	// }

	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("DomainResolve CreateOrder err=%v", err)
	// 	return
	// }
	// go ctrl.Biz.HandleOrder(orderID)

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.BuyCloudServerOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}

func (ctrl *OrderController) BuyCloudMysql(ctx context.Context, req *orderproto.BuyCloudMysqlReq) (resp *orderproto.BuyCloudMysqlResp, err error) {
	resp = new(orderproto.BuyCloudMysqlResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	req.ProposerEmail = userEmail
	applyMsg := req.ApplyMsg
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}
	// order := &orderdto.Order{
	// 	OrderType:     config.BuyCloudMysqlOrderTypeConf.ID,
	// 	Info:          string(orderInfoBytes),
	// 	ApplyMsg:      applyMsg,
	// 	ProposerEmail: userEmail,
	// }

	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("DomainResolve CreateOrder err=%v", err)
	// 	return
	// }
	// go ctrl.Biz.HandleOrder(orderID)
	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.BuyCloudMysqlOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}

func (ctrl *OrderController) BuyCloudRedis(ctx context.Context, req *orderproto.BuyCloudRedisReq) (
	resp *orderproto.BuyCloudRedisResp, err error) {
	resp = new(orderproto.BuyCloudRedisResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	req.ProposerEmail = userEmail
	applyMsg := req.ApplyMsg
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}
	// order := &orderdto.Order{
	// 	OrderType:     config.BuyCloudRedisOrderTypeConf.ID,
	// 	Info:          string(orderInfoBytes),
	// 	ApplyMsg:      applyMsg,
	// 	ProposerEmail: userEmail,
	// }

	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("DomainOrder CreateOrder err=%v", err)
	// 	return
	// }
	// go ctrl.Biz.HandleOrder(orderID)

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.BuyCloudRedisOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}

// 资源校验
func (ctrl *OrderController) CheckResouceDelete(ctx context.Context, req *orderproto.ResouceDeleteReq) (err error) {
	xlog := ctxlog.WithCtx(ctx)
	resourceDeletingFormArg := hook.ResourceDeletingFormArg{
		OpsAuditEmail: req.OpsAuditEmail,
		Supplier:      req.Supplier,
		Region:        req.Region,
		Type:          req.ResourceType,
		Name:          req.ResourceName,
		Id:            req.ResourceId,
	}
	err = hook.CheckDeletingResourceHandle2(ctx, resourceDeletingFormArg)
	if err != nil {
		xlog.Errorf("ResourceDeletingFormArg Marshal err=%v", err)
		return
	}
	return
}

// 资源销毁
func (ctrl *OrderController) ResouceDelete(ctx context.Context, req *orderproto.ResouceDeleteReq) (resp *orderproto.ResouceDeleteResp, err error) {
	xlog := ctxlog.WithCtx(ctx)
	resp = new(orderproto.ResouceDeleteResp)
	err = ctrl.CheckResouceDelete(ctx, req)
	if err != nil {
		xlog.Errorf("ResouceDelete err=%v", err)
		return
	}

	req.ApplyMsg = fmt.Sprintf("  销毁资源：\n\t  %s    %s \n\t 资源名称：%s \n\t 资源ID： %s",
		orderdto.SupplierMap[req.Supplier], orderdto.ResourceTypeMap[req.ResourceType],
		req.ResourceName, req.ResourceId)
	applyMsg := map[string]string{
		"ops_audit_email": req.OpsAuditEmail,
		"supplier":        strconv.FormatInt(int64(req.Supplier), 10),
		"resource_type":   req.ResourceType,
		"resource_name":   req.ResourceName,
		"resource_id":     req.ResourceId,
		"region":          req.Region,
	}
	orderInfoByte, err := json.Marshal(applyMsg)
	if err != nil {
		xlog.Errorf("ResouceDelete Marshal err=%v", err)
		return
	}
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	// order := &orderdto.Order{
	// 	OrderType:     config.ResourceDeleteOrderTypeConf.ID,
	// 	Info:          string(orderInfoByte),
	// 	ApplyMsg:      req.ApplyMsg,
	// 	ProposerEmail: userEmail,
	// }
	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	xlog.Errorf("ResouceDelete CreateOrder err=%v", err)
	// 	return
	// }
	// go ctrl.Biz.HandleOrder(orderID)

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.ResourceDeleteOrderTypeConf.ID,
		Info:          string(orderInfoByte),
		ApplyMsg:      req.ApplyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return

}

func (ctrl *OrderController) UpdateCMDBResourceServiceOwner(ctx context.Context, req *orderproto.UpdateCMDBResourceServiceOwnerReq) (
	resp *orderproto.UpdateCMDBResourceServiceOwnerResp, err error) {
	resp = new(orderproto.UpdateCMDBResourceServiceOwnerResp)
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}

	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	var msg string
	if len(req.Ids) == 0 {
		msg = fmt.Sprintf("资源交接人：%s\n资源接收人：%s\n交接资源ID：%s\n申请理由：%s", req.OldServiceOwner, req.NewServiceOwner, req.OldServiceOwner+"的所有资源", req.ApplyMsg)
	} else {
		ids := strings.Join(req.Ids, ",")
		msg = fmt.Sprintf("资源交接人：%s\n资源接收人：%s\n交接资源ID：%s\n申请理由：%s", req.OldServiceOwner, req.NewServiceOwner, ids, req.ApplyMsg)
	}

	// order := &orderdto.Order{
	// 	OrderType:     config.UpdateCMDBResourceServiceOwnerTypeConf.ID,
	// 	Info:          string(orderInfoBytes),
	// 	ApplyMsg:      msg,
	// 	ProposerEmail: userEmail,
	// }

	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("DomainOrder CreateOrder err=%v", err)
	// 	return
	// }
	// go ctrl.Biz.HandleOrder(orderID)

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.UpdateCMDBResourceServiceOwnerTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      msg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}
func (ctrl *OrderController) TokenApply(ctx context.Context, req *orderproto.TokenApplyReq) (resp *orderproto.TokenApplyResp, err error) {

	xlog := ctxlog.WithCtx(ctx)
	resp = new(orderproto.TokenApplyResp)

	if req.TokenType == "Namespace" {
		req.ApplyMsg = fmt.Sprintf("  k8s token 申请 \n\t 集群：  %s \n\t 空间：  %s   \n\t 类型：  %s \n\t 业务单元：%s",
			req.Cluster, req.Ns, req.TokenType, req.Path)
	} else {
		req.ApplyMsg = fmt.Sprintf("  k8s token 申请 \n\t 集群：  %s \n\t 空间：  %s  \n\t 名称：  %s\n\t 类型：  %s \n\t 业务单元：%s",
			req.Cluster, req.Ns, req.Name, req.TokenType, req.Path)
	}

	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	applyMsg := map[string]string{
		"ops_audit_email":    req.OpsAuditEmail,
		"cost_model_node_id": strconv.FormatInt(int64(req.CostModelNodeId), 10),
		"cluster":            req.Cluster,
		"token_type":         req.TokenType,
		"ns":                 req.Ns,
		"name":               req.Name,
		"path":               req.Path,
		"proposer_email":     userEmail,
	}
	orderInfoByte, err := json.Marshal(applyMsg)
	if err != nil {
		xlog.Errorf("TokenApply Marshal err=%v", err)
		return
	}

	// order := &orderdto.Order{
	// 	OrderType:     config.TokenApplyOrderTypeConf.ID,
	// 	Info:          string(orderInfoByte),
	// 	ApplyMsg:      req.ApplyMsg,
	// 	ProposerEmail: userEmail,
	// }
	// orderID, err := ctrl.Biz.CreateOrder(ctx, order)
	// if err != nil {
	// 	xlog.Errorf("TokenApply CreateOrder err=%v", err)
	// 	return
	// }
	// go ctrl.Biz.HandleOrder(orderID)
	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.TokenApplyOrderTypeConf.ID,
		Info:          string(orderInfoByte),
		ApplyMsg:      req.ApplyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}
	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("CommonOrder NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}

func (ctrl *OrderController) ModifyModelNode(ctx context.Context, req *orderproto.ModifyModelNodeReq) (resp *orderproto.ModifyModelNodeResp, err error) {
	resp = new(orderproto.ModifyModelNodeResp)

	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	applyMsg := req.ApplyMsg
	req.ApplyMsg = ""
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.ModifyModelNodeOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}

	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("ModifyModelNode NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("ModifyModelNode NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}
func (ctrl *OrderController) DeleteModelNode(ctx context.Context, req *orderproto.DeleteModelNodeReq) (resp *orderproto.DeleteModelNodeResp, err error) {
	resp = new(orderproto.DeleteModelNodeResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	applyMsg := req.ApplyMsg
	req.ApplyMsg = ""
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.DeleteModelNodeOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}

	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("ModifyModelNode NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("ModifyModelNode NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}
func (ctrl *OrderController) DragModelNode(ctx context.Context, req *orderproto.DragModelNodeReq) (resp *orderproto.DragModelNodeResp, err error) {
	resp = new(orderproto.DragModelNodeResp)
	userEmail := ctxutil.GetReqInfo(ctx).Get("userEmail").(string)
	applyMsg := req.ApplyMsg
	req.ApplyMsg = ""
	orderInfoBytes, err := json.Marshal(req)
	if err != nil {
		return
	}

	// 创建工单ID
	h := md5.New()
	h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
	orderID := hex.EncodeToString(h.Sum(nil))[8:24]

	order := &orderdto.Order{
		OrderID:       orderID,
		OrderType:     config.DragModelNodeOrderTypeConf.ID,
		Info:          string(orderInfoBytes),
		ApplyMsg:      applyMsg,
		ProposerEmail: userEmail,
		CTime:         time.Now(),
	}

	err = ctrl.Biz.NewCreateOrder(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("ModifyModelNode NewCreateOrder err=%v", err)
		return
	}

	go func(order *orderdto.Order) {
		err = ctrl.Biz.NewCreateStage(order, nil)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("ModifyModelNode NewCreateStage err=%v", err)
			return
		}
	}(order)
	return
}

// ValidateUser 用户校验API
func (ctrl *OrderController) ValidateUser(ctx context.Context, req *orderproto.ValidateUserReq) (resp *orderproto.ValidateUserResp, err error) {
	resp = new(orderproto.ValidateUserResp)

	// 参数校验
	if req.Email == "" {
		ctxlog.WithCtx(ctx).Warnf("ValidateUser failed: email is empty")
		resp.RespCommon.Ret = int32(orderutil.ApproveEmailErr.Ret)
		resp.RespCommon.Msg = orderutil.ApproveEmailErr.Msg
		return
	}

	// 调用Service层进行用户校验
	validatedUserInfo, err := ctrl.Biz.ValidateUserAndBuildInfo(ctx, req.Email)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("ValidateUser failed: %v", err)
		resp.RespCommon.Ret = int32(orderutil.ApproveEmailErr.Ret)
		resp.RespCommon.Msg = orderutil.ApproveEmailErr.Msg
		return
	}

	// 组装返回结果
	resp.Name = validatedUserInfo.Name
	resp.Department = validatedUserInfo.Department
	resp.OpenId = validatedUserInfo.OpenID

	ctxlog.WithCtx(ctx).Infof("ValidateUser success for email: %s", req.Email)
	return
}
