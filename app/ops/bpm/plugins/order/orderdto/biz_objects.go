package orderdto

import (
	"time"

	"aix.zhhainiao.com/app/ops/bpm/plugins/feishuservice/feishuservicedto"
)

const (
	FEISHU_CALLBACK_POLL_TIME_SPACE = 1 * time.Second
	AUDIT_TIME_OUT_SEC              = 60 * 60 * 24 * 14
)

const (
	NOT_EXIGENCY        = 1         // 普通工单
	EXIGENCY            = 2         // 加急工单
	STAGE_TYPE_AUDIT    = "audit"   // 审批阶段类型
	STAGE_TYPE_IF_AUDIT = "ifaudit" // 条件审批，如果不符合条件则不用审批
	STAGE_TYPE_EXCUTE   = "excute"  // 执行阶段类型
	STAGE_TYPE_CONFIRM  = "confirm" // 工单确认

	APPROVAL_THREE_LEVEL_DEFAULT_EMAIL = "<EMAIL>"
)

// RoleType 定义了用户在工单中的角色
type RoleType string

const (
	RoleToBeApproved    RoleType = "TO_BE_APPROVED"   // [待审] - 审批工单
	RoleWillBeApproved  RoleType = "WILL_BE_APPROVED" // [将审] - 审批工单
	RoleAlreadyApproved RoleType = "ALREADY_APPROVED" // [已审] - 审批工单
	RoleCompleted       RoleType = "COMPLETED"        // [完结] - 审批工单
	RoleApplicant       RoleType = "APPLICANT"        // [申请] - 进行中工单、完结工单
	RoleCcToMe          RoleType = "CC_TO_ME"         // [抄送] - 所有页面通用
)

// 工单与阶段任务结果
const (
	ORDER_RESUTL_DONE       = 1 // 工单状态: 已完成
	ORDER_RESUTL_REJECT     = 2 // 工单状态：驳回
	ORDER_RESUTL_FAIL       = 3 // 工单状态：失败
	ORDER_RESUTL_TIMEOUT    = 4 // 工单状态：超时
	STAGE_RESUTL_DONE       = 1 // 工单阶段任务状态: 已完成
	STAGE_RESUTL_REJECT     = 2 // 工单阶段任务状态：驳回
	STAGE_RESUTL_FAIL       = 3 // 工单阶段任务状态：失败
	STAGE_RESUTL_TIMEOUT    = 4 // 工单阶段任务状态：超时
	ORDER_RESUTL_DONE_MSG   = "工单已完成"
	ORDER_RESUTL_REJECT_MSG = "工单被驳回"
	ORDER_RESUTL_FAIL_MSG   = "工单异常结束[err:%s]"
	STAGE_RESUTL_DONE_MSG   = "OK"
	STAGE_RESUTL_REJECT_MSG = "驳回"
	STAGE_RESUTL_FAIL_MSG   = "工单阶段异常结束[err:%s]"
)

var OrderResults = []string{"工单进行中", "工单已完成", "工单被驳回", "工单异常结束", "工单超时"}
var OrderStaegResults = []string{"任务进行中", "已完成", "驳回或执行失败", "超时"}

var SupplierMap = map[int32]string{
	1: "腾讯云",
	2: "金山云",
	3: "阿里云",
	4: "华为云·聚云",
	6: "华为云·豹趣",
}
var ResourceTypeMap = map[string]string{
	"server": "服务器",
	"mysql":  "数据库",
	"redis":  "reids",
}

// 飞书人员信息
type PersonInfo struct {
	DepartmentInfo feishuservicedto.Department `json:"DepartmentInfo"`
	UserInfo       feishuservicedto.User       `json:"UserInfo"`
}

type Order struct {
	OrderID       string
	OrderType     string
	Title         string
	Info          string
	Exigency      int64     `json:"Exigency"`
	ProposerEmail string    `json:"ApplyEmail"`
	ApplyMsg      string    `json:"ApplyMsg"`
	OpsOwnerEmail string    `json:"OpsOwnerEmail"`
	CTime         time.Time `json:"CTime"`
	// 迭代二再实现：用户角色类型
	RoleType string `json:"role_type,omitempty"` // proposer(申请人)、auditor(审批人)、cc(抄送人)
}

// 金山云服务器申请信息
type KsServerApplyOrderInfo struct {
	Region             string `json:"Region"`
	Zone               string `json:"Zone"`
	Image              string `json:"Image"`
	HardwareSpec       string `json:"HardwareSpec"`
	HardwareSpecDetail string `json:"HardwareSpecDetail"`
	SysDiskType        string `json:"SysDiskType"`
	DataDiskType       string `json:"DataDiskType"`
	DataDiskSize       int64  `json:"DataDiskSize"`
	ChargeType         string `json:"ChargeType"`
	InstanceName       string `json:"InstanceName"`
	BusinessTreeBranch string `json:"BusinessTreeBranch"`
}

// 服务器jumpserver表单信息
type ServerJumpImpowerOrderInfo struct {
	Name         string   `json:"name"`
	ServerIP     []string `json:"server_ip"`
	ServiceOwner string   `json:"service_owner"`
	// ServiceOpsOwner []string `json:"service_ops_owner"`
	ImpowerType   int64  `json:"impower_type"`
	DayNum        int64  `json:"day_num"`
	ProposerEmail string `json:"proposer_email"`
}

// 服务器jumpserver表单信息
type BusinessTreeNodeOrderInfo struct {
	ProductOwner  string `json:"product_owner"`
	DevelopOwner  string `json:"develop_owner"`
	BusinessOwner string `json:"business_owner"`
	ParentId      int32  `json:"parent_id"`
	AuditOwner    string `json:"audit_owner"`
}

type OrderCommonInfo struct {
	Id            int64
	OrderId       string
	OrderType     string
	Info          string
	Exigency      int64
	ProposerEmail string
	ApplyMsg      string
	OpsOwnerEmail string
	TotalStageNum int64
	CurrentStage  int64
	Result        int64
	ResultMsg     string
	IsDel         int64
	Ctime         time.Time
	Mtime         time.Time
}

type OrderInfo struct {
	OrderID             string
	OrderType           string
	OrderTypeName       string
	TotalStageNum       int64
	CurrentStage        int64
	CurrentStageName    string
	CurrentOperator     string
	ProposerEmail       string
	OpsOwnerEmail       string
	ApplyMsg            string
	Info                string
	Result              int64
	ResultDesc          string
	ResultMsg           string
	ApplyDatetime       string
	LastUpdatedDatetime string // 用户在该工单中的角色
	IsCc                int    `json:"is_cc"`                  // 抄送状态 (0/1)
	ApprovalStatusForMe int    `json:"approval_status_for_me"` // 审批状态优先级 (0-无关联, 1-已审, 2-将审, 3-待审)
}

// biz层 数据库 stage 对象
type StageRecord struct {
	StageNum            int64
	StageName           string
	StageType           string
	StageOperator       string
	FeishuOpenId        string
	StageResult         int64
	StageResultDesc     string
	StageResultMsg      string
	ApplyDatetime       string
	LastUpdatedDatetime string
	Info                string
	StagePattern        string
}

// 数据库 stage表 info字段 对象
type StageInfo struct {
	FeiShuMsgID              string `json:"feishu_msg_id"`
	FeiShuSendMsgCradTimeSec int64  `json:"feishu_send_msg_card_time"`
	FeiShuUserEmail          string `json:"feishu_user_email"`
	FeiShuUserOpenID         string `json:"feishu_user_open_id"`
	FeiShuUserID             string `json:"feishu_user_id"`
}

// 需要资源拥有者审批的订单信息
type ResourceOwnerOrderInfo struct {
	ServerIP []string `json:"server_ip"`
}

type ResourceDeleteInfo struct {
	Id            string `json:"resource_id"`
	Supplier      string `json:"supplier"`
	Region        string `json:"region"`
	ResourceType  string `json:"resource_type"`
	ResourceName  string `json:"resource_name"`
	OpsAuditEmail string `json:"ops_audit_email"`
}

type DomainApplyInfo struct {
	Domain             string `json:"domain"`
	Whois              string `json:"whois"`
	Describe           string `json:"describe"`
	BuyTime            int32  `json:"buy_time"`
	BusinessTreeBranch string `json:"business_tree_branch"`
	BusinessOwner      string `json:"business_owner"`
	OpsAuditEmail      string `json:"ops_audit_email"`
}

type ResourceSupplyResult struct {
	CloudVendorResult  bool // 云厂商申请资源接口结果
	CmdbResourceResult bool // cmdb资源入库接口结果
	CmdbPwdResult      bool // cmdb资源密码入库接口结果
	CmdbBusinessResult bool // cmdb业务树挂载接口结果
}

// 审批/执行情况消息卡片内容
type AuditExecMsgCardBaseInfo struct {
	ProposerUserID             string
	ProposerDepartmentInfoName string
	OrderType                  string
	Ctime                      string
	ApplyReason                string
	AuditPeopleCardStr         string
	AuditStatusCardStr         string
	Param                      string
	StagesMap                  map[int]StageRecord
	AuditPersonInfoMap         map[string]PersonInfo
}

type TurnAuditOperatorParam struct {
	OrderID           string `json:"orderID"`
	StageNum          int64  `json:"stageNum"`
	StagePattern      string `json:"stagePattern"`
	MsgID             string `json:"msgID"`
	NewOperatorEmail  string `json:"newOperatorEmail"`
	NewOperatorOpenID string `json:"newOperatorOpenID"`
}

// MyAuditOrderSearchParam 我的审批工单搜索参数
type MyAuditOrderSearchParam struct {
	PageNum          int      `json:"page_num"`
	PageSize         int      `json:"page_size"`
	OrderId          string   `json:"order_id"`           // 工单号（模糊搜索）
	Title            string   `json:"title"`              // 工单标题（模糊搜索）
	AppliedStartDate *string  `json:"applied_start_date"` // 申请开始时间
	AppliedEndDate   *string  `json:"applied_end_date"`   // 申请结束时间
	OrderTypes       []string `json:"order_types"`        // 工单类型数组
	OperatorEmail    string   `json:"operator_email"`     // 操作者邮箱
	FilterByRole     []string `json:"filter_by_role"`     // 按角色筛选（迭代三预留）
}

// MyDoingOrderSearchParam 我的进行中工单搜索参数
type MyDoingOrderSearchParam struct {
	PageNum      int      `json:"page_num"`
	PageSize     int      `json:"page_size"`
	UserEmail    string   `json:"user_email"`     // 用户邮箱
	FilterByRole []string `json:"filter_by_role"` // 按角色筛选（迭代三预留）
}

// MyDoneOrderSearchParam 我的已完结工单搜索参数
type MyDoneOrderSearchParam struct {
	PageNum      int      `json:"page_num"`
	PageSize     int      `json:"page_size"`
	UserEmail    string   `json:"user_email"`     // 用户邮箱
	FilterByRole []string `json:"filter_by_role"` // 按角色筛选（迭代三预留）
}

// ValidatedUserInfo 用户校验返回信息
type ValidatedUserInfo struct {
	Name       string `json:"name"`
	Department string `json:"department"`
	OpenID     string `json:"open_id"`
}

// CcUserInfo 抄送用户信息
type CcUserInfo struct {
	CcOpenID string `json:"cc_open_id"`
	CcEmail  string `json:"cc_email"`
}

// AuditCardData 审批卡片数据结构
type AuditCardData struct {
	StagesMap                  map[int]interface{} `json:"stages_map"` // 使用interface{}避免循环导入
	AuditPepoleCardStr         string              `json:"audit_people_card_str"`
	AuditStatusCardStr         string              `json:"audit_status_card_str"`
	ProposerEmail              string              `json:"proposer_email"`
	ProposerUserID             string              `json:"proposer_user_id"`
	ProposerDepartmentInfoName string              `json:"proposer_department_info_name"`
	ParamJson                  []byte              `json:"param_json"`
}

// GetMyOrdersParam 列表查询参数（为迭代三预留）
type GetMyOrdersParam struct {
	FilterByRole []string `json:"filter_by_role"` // 迭代三再实现：按角色筛选
}

// OrderWithRole 带角色信息的工单结构（迭代二、三再实现）
type OrderWithRole struct {
	Order
	RoleType string `json:"role_type"` // 用户在该工单中的角色：proposer(申请人)、auditor(审批人)、cc(抄送人)
}

// CcNotificationInfo 抄送通知信息（迭代二再实现）
type CcNotificationInfo struct {
	OrderID      string `json:"order_id"`
	OrderTitle   string `json:"order_title"`
	OrderType    string `json:"order_type"`
	ProposerName string `json:"proposer_name"`
	CreateTime   string `json:"create_time"`
}

// OrderInfoWithRole 用于在应用层聚合时，存储一个工单及其所有关联角色
type OrderInfoWithRole struct {
	Order interface{}       // 复用已有的DAO层结构体，使用interface{}避免循环导入
	Roles map[RoleType]bool // 使用map作为set，存储该用户与此工单的所有角色关系
}
