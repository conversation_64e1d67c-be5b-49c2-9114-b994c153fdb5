package orderbiz

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	golocalerrors "errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"aix.zhhainiao.com/aixpublic/server/core/ctxlog"
	"aix.zhhainiao.com/aixpublic/utils/aixtime"
	"aix.zhhainiao.com/aixpublic/utils/inject"
	"aix.zhhainiao.com/app/ops/bpm/core/errors"
	"aix.zhhainiao.com/app/ops/bpm/plugins/auth/authutil"
	"aix.zhhainiao.com/app/ops/bpm/plugins/feishuhook/feishuhookdto"
	"aix.zhhainiao.com/app/ops/bpm/plugins/feishuservice"
	"aix.zhhainiao.com/app/ops/bpm/plugins/feishuservice/feishuservicedto"
	"aix.zhhainiao.com/app/ops/bpm/plugins/feishuservice/feishuserviceutil"
	"aix.zhhainiao.com/app/ops/bpm/plugins/sqlaudit/sqlauditbiz"
	"github.com/go-sql-driver/mysql"

	"aix.zhhainiao.com/app/ops/bpm/plugins/order/config"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/hook"

	"aix.zhhainiao.com/app/ops/bpm/plugins/order/orderutil"

	"aix.zhhainiao.com/app/ops/bpm/plugins/order/orderbiz/internal/orderdao"
	"aix.zhhainiao.com/app/ops/bpm/plugins/order/orderdto"
	"github.com/mitchellh/mapstructure"
)

var (
	defaultOrderBiz *OrderBiz
	_               inject.InjectIniter = defaultOrderBiz
)

// OrderBiz 必须实现 inject.InjectIniter 接口
type OrderBiz struct {
	Dao *orderdao.OrderDao `inject:""`
}

// 不能删除这个函数，MustInit会调用它创建对象
func NewOrderBiz() (*OrderBiz, error) {
	return &OrderBiz{}, nil
}

func GetInstance() *OrderBiz {
	return defaultOrderBiz
}

func MustInit() {
	var err error
	defaultOrderBiz, err = NewOrderBiz()
	if err != nil {
		panic(err)
	}

	inject.MustProvide(defaultOrderBiz)

	orderdao.MustInit()
}

func (biz *OrderBiz) Close() error {
	return nil
}

func (biz *OrderBiz) InjectInit() error {
	return nil
}

// CreateOrder 创建工单
func (biz *OrderBiz) CreateOrder(ctx context.Context, order *orderdto.Order) (orderID string, err error) {
	if len(order.OrderID) == 0 {
		// 生成order id
		// TODO 需要换一个工单id生成算法
		h := md5.New()
		h.Write([]byte(fmt.Sprint(time.Now().UnixNano())))
		order.OrderID = hex.EncodeToString(h.Sum(nil))[8:24]
	}
	orderID = order.OrderID

	// 获取订单类型配置信息
	orderTypeConf, ok := config.OrderTypeConfMap[order.OrderType]
	if !ok {
		ctxlog.WithCtx(ctx).Errorf("invalid orderTypeConf")
		return orderID, errors.IllegalOrderTypeErrorError
	}

	// // 新增工单确认逻辑
	// orderTypeConf.StageTypes = append(orderTypeConf.StageTypes, orderdto.STAGE_TYPE_CONFIRM)
	// orderTypeConf.StageNames = append(orderTypeConf.StageNames, "工单完成确认")
	// orderTypeConf.TotalStageNum += 1
	// orderTypeConf.StagePatterns = append(orderTypeConf.StagePatterns, hook.AUDIT_CONFIRM_PATTERN)

	// 写入数据库
	tbOrder := orderdao.TbOrder{
		OrderId:       order.OrderID,
		OrderType:     order.OrderType,
		TotalStageNum: int(orderTypeConf.TotalStageNum),
		Info:          order.Info,
		Exigency:      int(order.Exigency),
		ProposerEmail: order.ProposerEmail,
		OpsOwnerEmail: order.OpsOwnerEmail,
		ApplyMsg:      order.ApplyMsg,
	}
	_, err = biz.Dao.OrderMysql.NewOrder(ctx, tbOrder)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("CreateOrder Dao.OrderMysql.NewOrderTaks err=%v", err)
		return
	}
	return
}

// HandleOrder 处理工单（通过配置处理所有类型的工单）
func (biz *OrderBiz) HandleOrder(orderID string) {
	ctx := context.Background()
	defer func(ctx context.Context) {
		if err := recover(); err != nil {
			ctxlog.WithCtx(ctx).Errorf("handle order err = %v,but it has been recovered!!!", err)
		}
	}(ctx)

	var currentStage int64 = 0
	// 读取订单数据
	tbOrder, err := biz.Dao.OrderMysql.GetOrder(ctx, orderID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleKsServerApply Dao.OrderMysql.GetOrder err =%v", err)
		err = biz.UpdateOrderStageFail(ctx, tbOrder.OrderId, currentStage, err.Error())
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleFlowFail err=%v", err)
		}
		return
	}
	currentStage = int64(tbOrder.CurrentStage)
	orderCommonInfo := orderdto.OrderCommonInfo{
		Id:            int64(tbOrder.Id),
		OrderId:       tbOrder.OrderId,
		OrderType:     tbOrder.OrderType,
		Info:          tbOrder.Info,
		Exigency:      int64(tbOrder.Exigency),
		ProposerEmail: tbOrder.ProposerEmail,
		ApplyMsg:      tbOrder.ApplyMsg,
		OpsOwnerEmail: tbOrder.OpsOwnerEmail,
		TotalStageNum: int64(tbOrder.TotalStageNum),
		CurrentStage:  int64(tbOrder.CurrentStage),
		Result:        int64(tbOrder.Result),
		IsDel:         int64(tbOrder.IsDel),
		Ctime:         tbOrder.Ctime,
		Mtime:         tbOrder.Mtime,
	}
	// 获取不同工单的任务流配置信息
	orderTypeConf, ok := config.OrderTypeConfMap[orderCommonInfo.OrderType]
	if !ok {
		ctxlog.WithCtx(ctx).Errorf("invalid orderTypeConf")
		err = biz.UpdateOrderStageFail(ctx, tbOrder.OrderId, currentStage, err.Error())
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleFlowFail err=%v", err)
		}
		return
	}
	// 获取申请人个人、部门信息
	proposerInfo, err := biz.GetPersonInfo(ctx, orderCommonInfo.ProposerEmail)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleKsServerApply get proposerInfo err=%v", err)
		err = biz.UpdateOrderStageFail(ctx, tbOrder.OrderId, currentStage, err.Error())
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleFlowFail err=%v", err)
		}
		return
	}
	// 获取审批人员个人、部门信息
	auditPersonInfoMap, err := biz.GetAuditPersonInfoMap(ctx, &orderdto.Order{})
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleKsServerApply GetAuditPersonInfoMap err=%v", err)
		err = biz.UpdateOrderStageFail(ctx, tbOrder.OrderId, currentStage, err.Error())
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleFlowFail err=%v", err)
		}
		return
	}
	// 初始化订单流程阶段任务
	err = biz.InitOrderStages(ctx, orderCommonInfo.OrderId, orderTypeConf, auditPersonInfoMap)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleKsServerApply InitOrderStages err=%v", err)
		err = biz.UpdateOrderStageFail(ctx, tbOrder.OrderId, currentStage, err.Error())
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleFlowFail err=%v", err)
		}
		return
	}
	// 进入工单审批、执行流程
	flowInfo := hook.FlowInfo{
		OrderCommonInfo:    orderCommonInfo,
		OrderTypeConf:      orderTypeConf,
		ProposerInfo:       proposerInfo,
		AuditPersonInfoMap: auditPersonInfoMap,
	}
	currentStage, err = biz.FlowStages(ctx, flowInfo)
	proposerOpenID := flowInfo.ProposerInfo.UserInfo.OpenID
	if err != nil {
		flowErrMsg := err.Error()
		ctxlog.WithCtx(ctx).Errorf("FlowStages err=%s", flowErrMsg)

		err = biz.UpdateOrderStageFail(ctx, flowInfo.OrderCommonInfo.OrderId, currentStage, flowErrMsg)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleFlowFail err=%v", err)
		}

		// 发送工单顺利完结消息卡片
		orderFailMsgCard := feishuservice.GetProposerOrderFailMsgCard(orderID, orderTypeConf.Name, flowErrMsg)
		_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, proposerOpenID, orderFailMsgCard)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("send order ok msg card fail")
		}

		return
	}
	// 发送工单顺利完结消息卡片
	orderOkMsgCard := feishuservice.GetProposerOrderOkMsgCard(orderID, orderTypeConf.Name)
	_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, proposerOpenID, orderOkMsgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("send order ok msg card fail")
	}
}

// FlowStages 进入工单审批和执行阶段
func (biz *OrderBiz) FlowStages(ctx context.Context, flowInfo hook.FlowInfo) (currnetStage int64, err error) {
	orderID := flowInfo.OrderCommonInfo.OrderId
	currentStage := flowInfo.OrderCommonInfo.CurrentStage
	orderReulst := orderdto.ORDER_RESUTL_DONE
	// 重走被中断的工单流程时起作用
	if currentStage > 0 {
		currentStage -= 1
	}
	for i, sateType := range flowInfo.OrderTypeConf.StageTypes[currentStage:] {
		currentStage = int64(i + 1)
		// 更新数据库工单当前流程
		_, err := biz.Dao.OrderMysql.UpdateCurrentStage(ctx, flowInfo.OrderCommonInfo.OrderId, currentStage)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("FlowStages UpdateCurrentStage err=%v", err)
			return currentStage, err
		}
		// 进入审批处理流程
		if sateType == orderdto.STAGE_TYPE_AUDIT {
			isReject, err := biz.HandleAuditStage(ctx, flowInfo, currentStage)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("HandleAuditStage HandleAuditStage err=%v", err)
				return currentStage, err
			}
			if isReject {
				orderReulst = orderdto.ORDER_RESUTL_REJECT
				break
			}
		}
		// 进入执行流程
		var executeStageResultInfo string
		if sateType == orderdto.STAGE_TYPE_EXCUTE {
			executeStageResultInfo, err = biz.HandleExecuteStage(ctx, flowInfo, currentStage)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("HandleAuditStage HandleExecuteStage err=%v", err)
				return currentStage, err
			}
		}

		// 进入工单完成流程
		if sateType == orderdto.STAGE_TYPE_CONFIRM {
			err = biz.HandleConfirmStage(ctx, flowInfo, currentStage)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("HandleAuditStage HandleExecuteStage err=%v", err)
				return currentStage, err
			}
		}
		// 审批或执行通过，更新数据库stage表
		_, err = biz.Dao.StageMysql.UpdateStageResult(ctx, orderID, currentStage, orderdto.STAGE_RESUTL_DONE, executeStageResultInfo)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("AuditPassHandle UpdateStage err=%v", err)
			return currentStage, err
		}
	}
	// 工单完结，更新order表数据
	_, err = biz.Dao.OrderMysql.UpdateResult(ctx, flowInfo.OrderCommonInfo.OrderId, orderdto.OrderResults[orderReulst], int64(orderReulst))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FlowStages UpdateResult err=%v", err)
		return
	}
	// currnetStage = flowInfo.OrderCommonInfo.TotalStageNum
	return
}

// HandleAuditStage 处理工单审批
func (biz *OrderBiz) HandleAuditStage(ctx context.Context, flowInfo hook.FlowInfo, currentStage int64) (isReject bool, err error) {
	stageRecord, err := biz.Dao.StageMysql.GetStage(ctx, flowInfo.OrderCommonInfo.OrderId, currentStage)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleAuditStage StageMysql GetStage err=%v", err)
		return
	}
	stageInfo := &orderdto.StageInfo{}
	err = json.Unmarshal([]byte(stageRecord.Info), stageInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleAuditStage StageMysql Unmarshal stage info err=%v", err)
		return
	}

	// 提取工单信息
	orderID := flowInfo.OrderCommonInfo.OrderId
	orderApllyMsg := flowInfo.OrderCommonInfo.ApplyMsg
	orderTypeName := flowInfo.OrderTypeConf.Name
	// 提取申请人信息
	proposerOpenID := flowInfo.ProposerInfo.UserInfo.OpenID
	proposerName := flowInfo.ProposerInfo.UserInfo.Name
	proposerDepartmentName := flowInfo.ProposerInfo.DepartmentInfo.Name
	// 提取审批人信息
	auditPattern := flowInfo.OrderTypeConf.StagePatterns[currentStage-1] // 该阶段的审批模式
	// auditorOpenID := flowInfo.AuditPersonInfoMap[auditPattern].UserInfo.OpenID // 获取审批人飞书openID
	auditorName := flowInfo.AuditPersonInfoMap[auditPattern].UserInfo.Name
	// 构造审批消息卡片
	nowDateTime := time.Now().Format(aixtime.DefFmt)
	auditMsgCard := feishuservice.GetAuditMsgCard(proposerName, proposerDepartmentName, orderTypeName, nowDateTime, orderApllyMsg, orderID)

	var result bool
	if stageInfo.FeiShuMsgID != "" {
		result, err = biz.WaitForAuditCallBack(
			ctx, stageInfo.FeiShuSendMsgCradTimeSec, stageInfo.FeiShuMsgID, auditMsgCard, flowInfo, currentStage)
	} else {
		// 发送飞书审批卡片并等待结果
		result, err = biz.FeiShuAuditAndWait(ctx, auditMsgCard, flowInfo, currentStage)
	}
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FlowStages AuditAndWait err = %v", err)
		return
	}

	// 处理审批驳回
	if !result {
		isReject = true
		err = biz.UpdateOrderStageReject(ctx, orderID, currentStage)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("FlowStages UpdateOrderStageReject err=%v", err)
			return
		}
		// 发送驳回结果卡片
		rejectMsgCard := feishuservice.GetProposerRejectMsgCard(auditorName, orderID, orderTypeName)
		_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, proposerOpenID, rejectMsgCard)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("FlowStages audit reject send msg card err=%v", err)
			err = nil
		}
		return
	}
	isReject = false
	// 发送审批通过结果卡片
	agreeMsgCard := feishuservice.GetProposerAgreeMsgCard(auditorName, orderID, orderTypeName)
	_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, proposerOpenID, agreeMsgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FlowStages audit agree send msg card err=%v", err)
		err = nil
	}
	return
}

// HandleConfirmStage 处理工单完成确认
func (biz *OrderBiz) HandleConfirmStage(ctx context.Context, flowInfo hook.FlowInfo, currentStage int64) (err error) {
	// 提取工单信息
	orderID := flowInfo.OrderCommonInfo.OrderId
	orderApllyMsg := flowInfo.OrderCommonInfo.ApplyMsg
	orderTypeName := flowInfo.OrderTypeConf.Name
	// 提取申请人信息
	proposerOpenID := flowInfo.ProposerInfo.UserInfo.OpenID
	proposerName := flowInfo.ProposerInfo.UserInfo.Name
	proposerDepartmentName := flowInfo.ProposerInfo.DepartmentInfo.Name
	// 构造审批消息卡片
	nowDateTime := time.Now().Format(aixtime.DefFmt)
	auditMsgCard := feishuservice.GetConfirmMsgCard(proposerName, proposerDepartmentName, orderTypeName, nowDateTime, orderApllyMsg, orderID)

	// 发送飞书审批卡片并等待结果
	_, err = biz.FeiShuConfirmAndWait(ctx, auditMsgCard, flowInfo, currentStage)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage FlowStages AuditAndWait err = %v", err)
		return
	}

	// 发送审批通过结果卡片
	agreeMsgCard := feishuservice.GetCallbackConfirmMsgCard(orderID)
	_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, proposerOpenID, agreeMsgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage FlowStages audit agree send msg card err=%v", err)
		err = nil
	}
	return
}

// 处理工单执行
func (biz *OrderBiz) HandleExecuteStage(ctx context.Context, flowInfo hook.FlowInfo, currentStage int64) (stageInfo string, err error) {
	// 提取工单信息
	orderID := flowInfo.OrderCommonInfo.OrderId
	orderTypeName := flowInfo.OrderTypeConf.Name
	// 提取申请人信息
	proposerOpenID := flowInfo.ProposerInfo.UserInfo.OpenID
	// 提取执行模式和执行函数
	executePattern := flowInfo.OrderTypeConf.StagePatterns[currentStage-1]
	handleFunc, ok := config.ExecutorMap[executePattern]
	if !ok {
		ctxlog.WithCtx(ctx).
			Errorf("OrderTypeConf invaild execute pattern, currentStage %d, StagePatterns %+v, executePattern %s, ExecutorMap %+v",
				currentStage, flowInfo.OrderTypeConf.StagePatterns, executePattern, config.ExecutorMap)
		return stageInfo, errors.IllegalOrderTypeExecutePatternError
	}
	// 执行
	executeResultCode, executeResultInfo := handleFunc(ctx, flowInfo.OrderCommonInfo.Info)
	if executeResultCode != orderdto.STAGE_RESUTL_DONE {
		ctxlog.WithCtx(ctx).Errorf("FlowStages execute fail")
		return executeResultInfo, errors.OrderFlowExecuteError
	}
	// 发送执行成功消息卡片
	executeMsgCard := feishuservice.GetProposerExecuteOkMsgCard(orderID, orderTypeName)
	_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, proposerOpenID, executeMsgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FlowStages execute ok send msg card err=%v", err)
		err = nil
	}
	return
}

// GetPersonInfo 通过飞书获取个人信息（包括所在部门信息）
func (biz *OrderBiz) GetPersonInfo(ctx context.Context, proposerEmail string) (personInfo orderdto.PersonInfo, err error) {
	proposerUserInfo, err := feishuservice.Feishu.GetUserInfoByEmail(ctx, proposerEmail)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetPersonInfo Feishu.GetUserInfoByEmail err=%v", err)
		return
	}
	if len(proposerUserInfo.DepartmentIDS) == 0 {
		ctxlog.WithCtx(ctx).Errorf("proposerEmail:%s have no department", proposerEmail)
		err = feishuserviceutil.FeishuUserDepartmentError
		return
	}
	proposerDepartmentInfo, err := feishuservice.Feishu.GetDepartmentInfo(ctx, proposerUserInfo.DepartmentIDS[0])
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetPersonInfo Feishu.GetDepartmentInfo err=%v", err)
		return
	}
	personInfo.DepartmentInfo = proposerDepartmentInfo
	personInfo.UserInfo = proposerUserInfo
	return
}

// GetPersonInfo 通过工单关联审批人信息（包括所在部门信息） ***新增审批配置，修改该方法***
func (biz *OrderBiz) GetAuditPersonInfoMap(ctx context.Context, order *orderdto.Order) (auditPersonInfoMap map[string]orderdto.PersonInfo, err error) {
	stagePatterns := config.OrderTypeConfMap[order.OrderType].StagePatterns
	auditPersonInfoMap = make(map[string]orderdto.PersonInfo)
	// 根据工单配置返回审批人员信息
	for _, pattern := range stagePatterns {
		var getAuditInfoFunc hook.AuditHandle
		auditPatterns := strings.Split(pattern, ":")
		// 判断是否为不带后缀的审批模式
		if getFunc, ok := config.AuditPatternHandleMap[auditPatterns[0]]; ok && len(auditPatterns) == 1 {
			getAuditInfoFunc = getFunc
		} else if getFunc, ok := config.AuditIndexPatternHandleMap[auditPatterns[0]]; ok && len(auditPatterns) > 1 {
			getAuditInfoFunc = getFunc
		}
		if getAuditInfoFunc != nil {
			var auditInfo orderdto.PersonInfo
			auditInfo, err = getAuditInfoFunc(ctx, pattern, order)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("GetAuditPersonInfoMap AuditPatternHandleMap err=%v", err)
				return
			}
			auditPersonInfoMap[pattern] = auditInfo
		}
	}
	return
}

// InitOrderStages 初始化工单阶段任务表记录
func (biz *OrderBiz) InitOrderStages(ctx context.Context, orderID string, orderTypeConf hook.OrderTypeConf, auditPersonInfoMap map[string]orderdto.PersonInfo) (err error) {
	for i := 0; i < int(orderTypeConf.TotalStageNum); i++ {
		currentStage := i + 1
		thisStage := orderdao.TbStage{
			OrderId:      orderID,
			StageName:    orderTypeConf.StageNames[i],
			StageType:    orderTypeConf.StageTypes[i],
			StageNum:     currentStage,
			StagePattern: orderTypeConf.StagePatterns[i],
		}
		if info, ok := auditPersonInfoMap[orderTypeConf.StagePatterns[i]]; ok {
			ldapEmail, tmpErr := authutil.ConvertToLDAPEmail(ctx, info.UserInfo.Email)
			err = tmpErr
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("InitOrderStages ConvertToLDAPEmail err = %v", err)
				return
			}
			thisStage.Operator = ldapEmail
			thisStage.FeishuOpenId = info.UserInfo.OpenID
		} else {
			thisStage.Operator = config.DEFAULT_OPERATOR
		}
		_, err = biz.Dao.StageMysql.NewStage(ctx, thisStage)
		if err != nil {
			// 重复插入错误，忽略跳过。因为doing order recover时会发生这个错误
			if errMySQL, ok := err.(*mysql.MySQLError); ok {
				if errMySQL.Number == 1062 {
					err = nil
					continue
				}
			}
			ctxlog.WithCtx(ctx).Errorf("InitOrderStages NewStage err=%v", err)
			return
		}
	}
	return
}

// FeiShuConfirmAndWait 飞书发送确认卡片与等待回调结果
func (biz *OrderBiz) FeiShuConfirmAndWait(ctx context.Context, msgCard string, flowInfo hook.FlowInfo, currentStage int64) (result bool, err error) {
	auditPattern := flowInfo.OrderTypeConf.StagePatterns[currentStage-1]       // 该阶段的审批模式
	auditorOpenID := flowInfo.AuditPersonInfoMap[auditPattern].UserInfo.OpenID // 获取审批人飞书openID
	auditorUserID := flowInfo.AuditPersonInfoMap[auditPattern].UserInfo.UserID // 获取审批人飞书userID
	auditorEmail := flowInfo.AuditPersonInfoMap[auditPattern].UserInfo.Email   // 获取审批人飞书Email
	// 发出消息卡片
	msgID, sendMsgCardTimeSec, err := biz.FeiShuAudit(ctx, auditorOpenID, msgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FeiShuAudit err=%v,msgCard:%v", err, msgCard)
		return
	}

	biz.Dao.RedisFeishu.HMSet(msgID,
		map[string]string{
			feishuhookdto.SEND_MSG_CARD_TIME_SEC: fmt.Sprint(sendMsgCardTimeSec),
			feishuhookdto.USER_EMAIL:             auditorEmail,
			feishuhookdto.USER_ID:                auditorUserID,
			feishuhookdto.OPEN_ID:                auditorOpenID,
			feishuhookdto.ORDER_ID:               flowInfo.OrderCommonInfo.OrderId,
		})
	biz.Dao.RedisFeishu.Expire(msgID, orderdto.AUDIT_TIME_OUT_SEC*time.Second)

	// 写入飞书消息id到mysql stage表里
	stageInfo := &orderdto.StageInfo{
		FeiShuMsgID:              msgID,
		FeiShuSendMsgCradTimeSec: sendMsgCardTimeSec,
		FeiShuUserEmail:          auditorEmail,
		FeiShuUserOpenID:         auditorOpenID,
		FeiShuUserID:             auditorUserID,
	}
	stageInfoByte, err := json.Marshal(stageInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FeiShuAuditAndWait Marshal stageInfo err=%v", err)
		return
	}
	// auditEmail := feishuservice
	_, err = biz.Dao.StageMysql.UpdateStageInfo(ctx, flowInfo.OrderCommonInfo.OrderId, currentStage, string(stageInfoByte))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FeiShuAuditAndWait StageMysql UpdateStageInfo err=%v", err)
		return
	}

	// 轮询结果
	return biz.WaitForAuditCallBack(ctx, sendMsgCardTimeSec, msgID, msgCard, flowInfo, currentStage)
}

// AuditAndWait 飞书发送审核卡片与等待回调结果
func (biz *OrderBiz) FeiShuAuditAndWait(ctx context.Context, msgCard string, flowInfo hook.FlowInfo, currentStage int64) (result bool, err error) {
	auditPattern := flowInfo.OrderTypeConf.StagePatterns[currentStage-1]       // 该阶段的审批模式
	auditorOpenID := flowInfo.AuditPersonInfoMap[auditPattern].UserInfo.OpenID // 获取审批人飞书openID
	auditorUserID := flowInfo.AuditPersonInfoMap[auditPattern].UserInfo.UserID // 获取审批人飞书userID
	auditorEmail := flowInfo.AuditPersonInfoMap[auditPattern].UserInfo.Email   // 获取审批人飞书Email
	// 发出消息卡片
	msgID, sendMsgCardTimeSec, err := biz.FeiShuAudit(ctx, auditorOpenID, msgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FeiShuAudit err=%v,msgCard:%v", err, msgCard)
		return
	}
	// 写入飞书消息id到redis，用于飞书结果回调
	// biz.Dao.RedisFeishu.HSet(
	// 	msgID,
	// 	feishuhookdto.SEND_MSG_CARD_TIME_SEC,
	// 	sendMsgCardTimeSec)
	biz.Dao.RedisFeishu.HMSet(msgID,
		map[string]string{
			feishuhookdto.SEND_MSG_CARD_TIME_SEC: fmt.Sprint(sendMsgCardTimeSec),
			feishuhookdto.USER_EMAIL:             auditorEmail,
			feishuhookdto.USER_ID:                auditorUserID,
			feishuhookdto.OPEN_ID:                auditorOpenID,
			feishuhookdto.ORDER_ID:               flowInfo.OrderCommonInfo.OrderId,
		})
	biz.Dao.RedisFeishu.Expire(msgID, orderdto.AUDIT_TIME_OUT_SEC*time.Second)

	// 写入飞书消息id到mysql stage表里
	stageInfo := &orderdto.StageInfo{
		FeiShuMsgID:              msgID,
		FeiShuSendMsgCradTimeSec: sendMsgCardTimeSec,
		FeiShuUserEmail:          auditorEmail,
		FeiShuUserOpenID:         auditorOpenID,
		FeiShuUserID:             auditorUserID,
	}
	stageInfoByte, err := json.Marshal(stageInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FeiShuAuditAndWait Marshal stageInfo err=%v", err)
		return
	}
	// auditEmail := feishuservice
	_, err = biz.Dao.StageMysql.UpdateStageInfo(ctx, flowInfo.OrderCommonInfo.OrderId, currentStage, string(stageInfoByte))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FeiShuAuditAndWait StageMysql UpdateStageInfo err=%v", err)
		return
	}

	// 轮询结果
	return biz.WaitForAuditCallBack(ctx, sendMsgCardTimeSec, msgID, msgCard, flowInfo, currentStage)
}

// WaitForAuditCallBack 轮询查询审批回调结果
func (biz *OrderBiz) WaitForAuditCallBack(
	ctx context.Context, waitStartTimeSec int64, msgID, msgCard string, flowInfo hook.FlowInfo, currentStage int64) (
	result bool, err error) {
	for time.Now().Unix()-waitStartTimeSec < orderdto.AUDIT_TIME_OUT_SEC {
		callbackTimeSec := biz.Dao.RedisFeishu.HGet(msgID, feishuhookdto.CALLBACK_TIME_SEC).Val()
		if callbackTimeSec == "" {
			time.Sleep(orderdto.FEISHU_CALLBACK_POLL_TIME_SPACE)
			continue
		}
		// 判断是否转审批
		newOperatorEmail := biz.Dao.RedisFeishu.HGet(msgID, feishuhookdto.NEW_OPERATOR_EMAIL).Val()
		if newOperatorEmail != "" {
			auditPattern := flowInfo.OrderTypeConf.StagePatterns[currentStage-1] // 该阶段的审批模式
			oldAuditPersonInfo := flowInfo.AuditPersonInfoMap[auditPattern]
			// 修改成新的审批人信息
			auditPersonInfo, err := feishuservice.Feishu.GetUserInfoByEmail(ctx, newOperatorEmail)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("WaitForAuditCallBack turn stage operator GetUserInfoByEmail err = %v")
				return result, err
			}
			auditDepartmentInfo, err := feishuservice.Feishu.GetDepartmentInfo(ctx, auditPersonInfo.DepartmentIDS[0])
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("WaitForAuditCallBack turn stage operator GetUserInfoByOpenID err=%v", err)
				return result, err
			}
			auditInfo := orderdto.PersonInfo{
				DepartmentInfo: auditDepartmentInfo,
				UserInfo:       auditPersonInfo,
			}

			flowInfo.AuditPersonInfoMap[auditPattern] = auditInfo // 修改该阶段的审批人
			// todo 需要考虑是否需要修改order info
			// 更新 stage 审批人
			_, err = biz.Dao.StageMysql.UpdateOperator(ctx, newOperatorEmail, flowInfo.OrderCommonInfo.OrderId, currentStage)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("WaitForAuditCallBack turn stage operator UpdateOperator err=%v", err)
				return result, err
			}

			// 发送消息给申请人
			proposerOpenID := flowInfo.ProposerInfo.UserInfo.OpenID
			trunOperatorMsgCard := feishuservice.GetTrunOperatorMsgCard(
				flowInfo.OrderCommonInfo.OrderId,
				currentStage,
				newOperatorEmail)
			_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, proposerOpenID, trunOperatorMsgCard)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("FlowStages audit turn operator send roposer msg card err=%v", err)
				err = nil
			}

			// 发送消息给原审批人
			_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, oldAuditPersonInfo.UserInfo.OpenID, trunOperatorMsgCard)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("FlowStages audit turn operator send roposer msg card err=%v", err)
				err = nil
			}

			// 消除旧消息
			biz.Dao.RedisFeishu.Del(msgID)

			// 发送新的审批消息
			return biz.FeiShuAuditAndWait(ctx, msgCard, flowInfo, currentStage)

		}
		actionResutl := biz.Dao.RedisFeishu.HGet(
			msgID,
			feishuhookdto.ACTION_RESULT).Val()
		biz.Dao.RedisFeishu.Del(msgID) // 删除已经处理的回调信息
		if actionResutl == feishuservicedto.AUDIT_YES {
			return true, nil
		}
		return false, nil
	}
	err = errors.WaitAuditTimeoutError
	return
}

func (biz *OrderBiz) FeiShuAudit(ctx context.Context, userOpenID, msgCard string) (
	msgID string, sendMsgCardTimeSec int64, err error) {
	sendMsgCardTimeSec = time.Now().Unix()
	msgID, err = feishuservice.Feishu.SendMsgByOpenID(ctx, userOpenID, msgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("FeishuAuditAndWait SendMsgByOpenID err=%v", err)
		return
	}
	return
}

// HandleAuditReject 驳回情况更新数据库order,stage记录
func (biz *OrderBiz) UpdateOrderStageReject(ctx context.Context, orderID string, currentStage int64) (err error) {
	_, err = biz.Dao.StageMysql.UpdateStageResult(ctx, orderID, currentStage, orderdto.STAGE_RESUTL_REJECT, orderdto.STAGE_RESUTL_REJECT_MSG)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleAuditReject UpdateStage err=%v", err)
		return err
	}
	_, err = biz.Dao.OrderMysql.UpdateResult(ctx, orderID, orderdto.ORDER_RESUTL_REJECT_MSG, orderdto.ORDER_RESUTL_REJECT)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("AuditPassHandle UpdateCurrentStage err=%v", err)
		return err
	}
	return
}

// HandleFlowFail 工单流程出错情况更新数据库order,stage记录
func (biz *OrderBiz) UpdateOrderStageFail(ctx context.Context, orderID string, currentStage int64, errStr string) (err error) {
	if currentStage >= 1 {
		errStr = fmt.Sprintf(orderdto.STAGE_RESUTL_FAIL_MSG, errStr)
		_, err = biz.Dao.StageMysql.UpdateStageResult(ctx, orderID, currentStage, orderdto.STAGE_RESUTL_FAIL, errStr)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleFlowFail UpdateStage err=%v", err)
			err = nil
		}
	}
	errStr = fmt.Sprintf(orderdto.ORDER_RESUTL_FAIL_MSG, errStr)
	_, err = biz.Dao.OrderMysql.UpdateResult(ctx, orderID, errStr, orderdto.ORDER_RESUTL_FAIL)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleFlowFail UpdateResult err=%v", err)
	}
	return
}

func (biz *OrderBiz) GetDoneOrdersByPage(ctx context.Context, param *orderdto.MyDoneOrderSearchParam) (
	totalNum int64, orderInfos []orderdto.OrderInfo, err error) {

	// 构建DAO层参数
	daoParam := &orderdao.MyOrdersDAOParam{
		UserEmail:    param.UserEmail,
		PageNum:      param.PageNum,
		PageSize:     param.PageSize,
		FilterByRole: param.FilterByRole,
	}

	// 调用独立的已完结工单查询方法
	ordersWithRole, total, err := biz.Dao.GetMyDoneOrdersWithRole(ctx, daoParam)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetDoneOrdersByPage GetMyDoneOrdersWithRole failed: %v", err)
		return
	}

	totalNum = total

	// 使用抽象方法转换结果并计算最终角色
	orderInfos = biz.convertOrderWithRoleToOrderInfo(ordersWithRole)

	return
}

func (biz *OrderBiz) GetDoingOrdersByPage(ctx context.Context, param *orderdto.MyDoingOrderSearchParam) (
	totalNum int64, orderInfos []orderdto.OrderInfo, err error) {

	// 构建DAO层参数
	daoParam := &orderdao.MyOrdersDAOParam{
		UserEmail:    param.UserEmail,
		PageNum:      param.PageNum,
		PageSize:     param.PageSize,
		FilterByRole: param.FilterByRole,
	}

	// 调用独立的进行中工单查询方法
	ordersWithRole, total, err := biz.Dao.GetMyDoingOrdersWithRole(ctx, daoParam)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetDoingOrdersByPage GetMyDoingOrdersWithRole failed: %v", err)
		return
	}

	totalNum = total

	// 使用抽象方法转换结果并计算最终角色
	orderInfos = biz.convertOrderWithRoleToOrderInfo(ordersWithRole)

	return
}

func (biz *OrderBiz) GetOrderDetail(ctx context.Context, orderID string) (
	orderInfo orderdto.OrderInfo, stageRecords []orderdto.StageRecord, err error) {
	orderRecord, err := biz.Dao.OrderMysql.GetOrder(ctx, orderID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetOrderDetail OrderMysql.GetOrder err=%v", err)
	}
	orderInfo = orderdto.OrderInfo{
		OrderID:             orderRecord.OrderId,
		OrderType:           orderRecord.OrderType,
		OrderTypeName:       config.OrderTypeConfMap[orderRecord.OrderType].Name,
		TotalStageNum:       int64(orderRecord.TotalStageNum),
		CurrentStage:        int64(orderRecord.CurrentStage),
		ProposerEmail:       orderRecord.ProposerEmail,
		OpsOwnerEmail:       orderRecord.OpsOwnerEmail,
		ApplyMsg:            orderRecord.ApplyMsg,
		Info:                orderRecord.Info,
		Result:              int64(orderRecord.Result),
		ResultDesc:          orderdto.OrderResults[orderRecord.Result],
		ResultMsg:           orderRecord.ResultMsg,
		ApplyDatetime:       orderRecord.Ctime.Format(aixtime.DefFmt),
		LastUpdatedDatetime: orderRecord.Mtime.Format(aixtime.DefFmt),
	}
	stageDBRecords, err := biz.Dao.StageMysql.GetStages(ctx, orderID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetOrderDetail StageMysql.GetStages err=%v", err)
	}
	for _, item := range stageDBRecords {
		stageRecords = append(stageRecords, orderdto.StageRecord{
			StageNum:            int64(item.StageNum),
			StageName:           item.StageName,
			StageType:           item.StageType,
			StageOperator:       item.Operator,
			StageResult:         int64(item.Result),
			StageResultDesc:     orderdto.OrderStaegResults[item.Result],
			StageResultMsg:      item.ResultMsg,
			ApplyDatetime:       item.Ctime.Format(aixtime.DefFmt),
			LastUpdatedDatetime: item.Mtime.Format(aixtime.DefFmt),
		})
	}
	return
}

// GetMyAuditOrderWithSearch 获取我的审批工单（支持动态搜索条件）
func (biz *OrderBiz) GetMyAuditOrderWithSearch(
	ctx context.Context, param *orderdto.MyAuditOrderSearchParam) (
	totalNum int64, orderInfos []orderdto.OrderInfo, err error) {

	// 构建DAO层参数
	daoParam := &orderdao.MyAuditOrdersDAOParam{
		PageNum:          param.PageNum,
		PageSize:         param.PageSize,
		OrderId:          param.OrderId,
		Title:            param.Title,
		AppliedStartDate: param.AppliedStartDate,
		AppliedEndDate:   param.AppliedEndDate,
		OrderTypes:       param.OrderTypes,
		OperatorEmail:    param.OperatorEmail,
		FilterByRole:     param.FilterByRole,
	}

	// 调用新的gorm实现的审批工单查询方法
	ordersWithRole, total, err := biz.Dao.GetMyAuditOrdersWithRole(ctx, daoParam)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyAuditOrderWithSearch GetMyAuditOrdersWithRole err=%v", err)
		return
	}
	totalNum = total

	// 使用抽象方法转换结果并计算最终角色
	orderInfos = biz.convertOrderWithRoleToOrderInfo(ordersWithRole)

	return
}

// GetAuditStageInfoByMysql 获取mysql审批信息
func (biz *OrderBiz) GetAuditStageInfoByMysql(ctx context.Context, orderID string, stageNum int64) (
	stageAuditInfo *orderdto.StageInfo, err error) {
	record, err := biz.Dao.StageMysql.GetStage(ctx, orderID, stageNum)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMsgID Dao.StageMysql.GetStage err = %v", err)
		return
	}
	if record.StageType != orderdto.STAGE_TYPE_AUDIT {
		err = orderutil.StageAuditTypeErr
		return
	}
	err = json.Unmarshal([]byte(record.Info), &stageAuditInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMsgID Unmarshal err = %v", err)
		return
	}
	return
}

// GetStageAuditInfoByRedis 获取Redis审批信息
func (biz *OrderBiz) GetStageAuditInfoByRedis(ctx context.Context, msgID string) (stageAuditInfo *orderdto.StageInfo, err error) {
	auditStrInfo := biz.Dao.RedisFeishu.HGetAll(msgID).Val()
	err = mapstructure.Decode(auditStrInfo, auditStrInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMsgID mapstructure err = %v", err)
		return
	}
	return
}

func (biz *OrderBiz) GetStageInfoWithAuditInfo(ctx context.Context, orderID string, stageNum int64) (
	stageInfo *orderdao.TbStage, stageAuditInfo *orderdto.StageInfo, err error) {
	stageInfo, err = biz.Dao.StageMysql.GetStage(ctx, orderID, stageNum)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("IsStageOperator GetStage err = %v", err)
		return
	}
	if stageInfo.StageType != orderdto.STAGE_TYPE_AUDIT {
		err = orderutil.StageAuditTypeErr
		return
	}
	err = json.Unmarshal([]byte(stageInfo.Info), &stageAuditInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMsgID Unmarshal err = %v", err)
		return
	}
	return

}

// GetAuditOrderTypesByOperator 获取用户作为审批人的所有工单类型
func (biz *OrderBiz) GetAuditOrderTypesByOperator(ctx context.Context, operatorEmail string) (orderTypes []string, err error) {
	orderTypes, err = biz.Dao.OrderMysql.GetAuditOrderByOperatorEmail(ctx, operatorEmail)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetAuditOrderTypesByOperator err = %v", err)
		return
	}
	return
}

func (biz *OrderBiz) GetStageInfosWithAuditInfos(ctx context.Context, orderID string) (
	stageInfos []orderdao.TbStage, stageAuditInfos []orderdto.StageInfo, err error) {
	stageInfos, err = biz.Dao.StageMysql.GetStages(ctx, orderID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetStageInfosWithAuditInfos GetStages err = %v", err)
		return
	}
	for _, stageInfo := range stageInfos {
		var stageAuditInfo orderdto.StageInfo
		err = json.Unmarshal([]byte(stageInfo.Info), &stageAuditInfo)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("GetStageInfosWithAuditInfos Unmarshal err = %v", err)
			return
		}
		stageAuditInfos = append(stageAuditInfos, stageAuditInfo)
	}
	return

}

// TurnOperatorByRedis 更新Redis中的操作人
func (biz *OrderBiz) TurnOperatorByRedis(ctx context.Context, msgID, newOperatorEmail string) {
	biz.Dao.RedisFeishu.HSet(msgID, feishuhookdto.NEW_OPERATOR_EMAIL, newOperatorEmail)
	biz.Dao.RedisFeishu.HSet(
		msgID, feishuhookdto.CALLBACK_TIME_SEC,
		strconv.FormatInt(time.Now().Unix(), 10))
}

// TurnAuditOperator 变更审批人完整逻辑
func (biz *OrderBiz) TurnAuditOperator(ctx context.Context, orderID, newOperatorEmail string,
	stageNum int64) (stageAuditInfos []orderdto.StageInfo, errback error) {
	// 获取当前阶段信息和审批信息
	stageInfos, stageAuditInfos, err := biz.GetStageInfosWithAuditInfos(ctx, orderID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("TurnAuditOperator GetStageInfosWithAuditInfos err = %v", err)
		errback = orderutil.TurnAuditOperatorFailErr
		return
	}

	// 目前前端详情页面没有自动刷新，导致能请求变更已完成阶段的审批人-2025-7-8
	if stageInfos[stageNum-1].Result != 0 {
		ctxlog.WithCtx(ctx).Errorf("TurnAuditOperator stage %d is already completed", stageNum)
		errback = orderutil.AlreadyAuditErr
		return
	}

	// 验证新审批人是否存在
	newOperatorInfo, err := feishuservice.Feishu.GetUserInfoByEmail(ctx, newOperatorEmail)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("TurnAuditOperator GetUserInfoByEmail err = %v", err)
		errback = orderutil.ApproveEmailErr
		return
	}

	// 执行变更审批人逻辑
	// 更新数据库Stage表中的操作人
	_, err = biz.Dao.StageMysql.UpdateOperatorAndOpenID(ctx, newOperatorEmail,
		newOperatorInfo.OpenID, orderID, stageNum)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("TurnAuditOperator UpdateOperator err = %v", err)
		errback = orderutil.TurnAuditOperatorFailErr
		return
	}

	// 如果是运维审批阶段，还需更新工单表中的运维负责人
	if stageInfos[stageNum-1].StagePattern == hook.AUDIT_POINT_DYNAMIC_EMAIL_INDEX_PATTREN {
		_, err = biz.Dao.OrderMysql.UpdateOrderOpsOwner(ctx, orderID, newOperatorEmail)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("TurnAuditOperator UpdateOrderOpsOwner err = %v", err)
			errback = orderutil.TurnAuditOperatorFailErr
			return
		}
	}

	return stageAuditInfos, nil
}

// 变更审批人发送消息卡片
func (biz *OrderBiz) SendMsgCardForTurnFlowAudit(ctx context.Context, orderID string, stageNum int64,
	newOperatorEmail string, stageAuditInfos []orderdto.StageInfo) error {
	// 获取审批/执行情况消息卡片基础内容
	msgCard, err := biz.MakeAuditExecMsgCard(ctx, orderID, stageNum)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("SendMsgCardForTurnFlowAudit MakeAuditExecMsgCard err = %v", err)
		return err
	}

	// 组装变更审批人消息卡片
	// 发送变更审批人消息卡片以及置灰之前的消息卡片
	err = biz.SendTurnAuditMsgCardAndGray(ctx, orderID, stageNum, newOperatorEmail, stageAuditInfos, msgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("SendMsgCardForTurnFlowAudit SendTurnAuditMsgCardAndGray err = %v", err)
		return err
	}

	// 组装审批消息卡片
	// 发送审批消息卡片给新审批人
	err = biz.SendAuditMsgCard(ctx, stageNum, orderID, msgCard)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("SendMsgCardForTurnFlowAudit SendAuditMsgCard err = %v", err)
		return err
	}
	return nil
}

// 组装、发送变更审批人消息卡片以及置灰之前的消息卡片
func (biz *OrderBiz) SendTurnAuditMsgCardAndGray(ctx context.Context, orderID string, stageNum int64,
	newOperatorEmail string, stageAuditInfos []orderdto.StageInfo, msgCard *orderdto.AuditExecMsgCardBaseInfo) (err error) {
	stageAuditInfosLen := len(stageAuditInfos)
	if stageAuditInfosLen == 0 {
		return orderutil.TurnAuditOperatorFailErr
	}
	uniqueOpenIDsToSend := make(map[string]struct{})
	// 组装变更审批人消息卡片
	turnOperatorMsgCard := feishuservice.GetTrunOperatorMsgCard(orderID, stageNum, newOperatorEmail)
	// 提交者
	if stageAuditInfos[0].FeiShuUserOpenID != "" {
		uniqueOpenIDsToSend[stageAuditInfos[0].FeiShuUserOpenID] = struct{}{}
	} else {
		ctxlog.WithCtx(ctx).Warnf("SendTurnAuditMsgCardAndGray Proposer OpenID is empty")
	}
	// 当前节点之前的所有节点
	if stageNum > 2 {
		for i, stageAuditInfo := range stageAuditInfos[1 : stageNum-1] {
			if stageAuditInfo.FeiShuUserOpenID == "" {
				ctxlog.WithCtx(ctx).Warnf("SendTurnAuditMsgCardAndGray Stage %d OpenID is empty", i+1)
				continue
			}
			uniqueOpenIDsToSend[stageAuditInfo.FeiShuUserOpenID] = struct{}{}
		}
	}
	// 原审批人
	if stageNum > 1 && stageAuditInfos[stageNum-1].FeiShuUserOpenID != "" {
		uniqueOpenIDsToSend[stageAuditInfos[stageNum-1].FeiShuUserOpenID] = struct{}{}
	} else if stageNum > 1 {
		ctxlog.WithCtx(ctx).Warnf("SendTurnAuditMsgCardAndGray Original approver OpenID is empty")
	}
	// 发送变更审批人消息卡片给提交者、当前节点之前的所有节点和原审批人
	for openID := range uniqueOpenIDsToSend {
		if _, err := feishuservice.Feishu.SendMsgByOpenID(ctx, openID, turnOperatorMsgCard); err != nil {
			ctxlog.WithCtx(ctx).Errorf("Failed to send turnOperatorMsgCard to OpenID %s err = %v", openID, err)
			return err
		}
	}
	// 置灰原审批人的审批消息卡片
	baseAuditMsgCard := &feishuservicedto.NewAuditMsgCard{
		ProposerUserID:             msgCard.ProposerUserID,
		ProposerDepartmentInfoName: msgCard.ProposerDepartmentInfoName,
		OrderType:                  msgCard.OrderType,
		Ctime:                      msgCard.Ctime,
		ApplyReason:                msgCard.ApplyReason,
		AuditPeopleCardStr:         msgCard.AuditPeopleCardStr,
		AuditStatusCardStr:         msgCard.AuditStatusCardStr,
		AgreeButton:                true,
		Param:                      string(msgCard.Param),
		RejectButton:               true,
		OrderTitle:                 "你有一条工单待审批(" + orderID + ")",
		OrderTitleColor:            "blue",
	}
	auditMsgCard := feishuservice.GetNewAuditMsgCard(baseAuditMsgCard)
	if err = feishuservice.Feishu.UpdateFeishuMessage(ctx, auditMsgCard, stageAuditInfos[stageNum-1].FeiShuMsgID); err != nil {
		return biz.handleAuditExecError(ctx, orderID, stageNum, err, "SendTurnAuditMsgCardAndGray", "UpdateFeishuMessage")
	}
	// 发送变更后的执行情况消息卡片给最后确认者
	baseAuditMsgCard.OrderTitle = fmt.Sprintf("工单(%s)执行情况!", orderID)
	execInfoCardStr := feishuservice.GetNewAuditMsgCard(baseAuditMsgCard)
	if err = feishuservice.Feishu.UpdateFeishuMessage(ctx, execInfoCardStr, stageAuditInfos[stageAuditInfosLen-1].FeiShuMsgID); err != nil {
		return biz.handleAuditExecError(ctx, orderID, stageNum, err, "SendTurnAuditMsgCardAndGray", "UpdateFeishuMessage")
	}
	// 更新阶段信息中的消息ID
	stageInfoJsonStr, err := MakeStageInfoJsonByStr(ctx, stageAuditInfos[stageAuditInfosLen-1].FeiShuMsgID, time.Now().Unix(),
		stageAuditInfos[stageAuditInfosLen-1].FeiShuUserEmail, stageAuditInfos[stageAuditInfosLen-1].FeiShuUserOpenID,
		stageAuditInfos[stageAuditInfosLen-1].FeiShuUserID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("SendTurnAuditMsgCardAndGray MakeStageInfoJsonByStr err=%v", err)
		return
	}
	if _, err = biz.Dao.StageMysql.UpdateStageInfo(ctx, orderID, int64(stageAuditInfosLen), stageInfoJsonStr); err != nil {
		return biz.handleAuditExecError(ctx, orderID, stageNum, err, "SendTurnAuditMsgCardAndGray", "UpdateStageInfo")
	}
	return
}

// SendAuditMsgCard 发送审批消息卡片
// 基于工单ID和阶段号，获取相关信息并发送审批消息卡片给对应阶段的审批人
func (biz *OrderBiz) SendAuditMsgCard(ctx context.Context, stageNum int64, orderID string, msgCard *orderdto.AuditExecMsgCardBaseInfo) (err error) {
	newAuditMsgCard := &feishuservicedto.NewAuditMsgCard{
		ProposerUserID:             msgCard.ProposerUserID,
		ProposerDepartmentInfoName: msgCard.ProposerDepartmentInfoName,
		OrderType:                  msgCard.OrderType,
		Ctime:                      msgCard.Ctime,
		ApplyReason:                msgCard.ApplyReason,
		AuditPeopleCardStr:         msgCard.AuditPeopleCardStr,
		AuditStatusCardStr:         msgCard.AuditStatusCardStr,
		AgreeButton:                false,
		Param:                      string(msgCard.Param),
		RejectButton:               false,
		OrderTitle:                 "你有一条工单待审批(" + orderID + ")",
		OrderTitleColor:            "blue",
	}
	auditBaseAuditCardStr := feishuservice.GetNewAuditMsgCard(newAuditMsgCard)

	auditMsgID, _, err := biz.FeiShuAudit(ctx, msgCard.StagesMap[int(stageNum)].FeishuOpenId, auditBaseAuditCardStr)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("SendAuditMsgCard FeiShuAudit err=%v", err)
		return
	}

	// 更新阶段信息中的消息ID
	auditPattern := msgCard.StagesMap[int(stageNum)].StagePattern // 该阶段的审批模式
	stageInfoJsonStr, err := MakeStageInfoJsonByPattern(ctx, auditMsgID, time.Now().Unix(), auditPattern, msgCard.AuditPersonInfoMap)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("SendAuditMsgCard MakeStageInfoJsonByPattern err=%v", err)
		return
	}
	_, err = biz.Dao.StageMysql.UpdateStageInfo(ctx, orderID, stageNum, stageInfoJsonStr)
	if err != nil {
		return biz.handleAuditExecError(ctx, orderID, stageNum, err, "SendAuditMsgCard", "UpdateStageInfo")
	}

	return nil
}

// GetDoneOrderByTimeAndType 获取已完成的工单通过mtime和工单类型获取
func (biz *OrderBiz) GetDoneOrderByTimeAndType(ctx context.Context, mtime, orderType string) ([]orderdao.TbOrder, error) {
	res, err := biz.Dao.OrderMysql.GetDoneOrderByTypeAndTime(ctx, mtime, orderType)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetDoneOrderByTimeAndType err=%v", err)
		return nil, err
	}
	return res, nil
}

// NewCreateOrder 创建工单
func (biz *OrderBiz) NewCreateOrder(ctx context.Context, order *orderdto.Order) (err error) {
	// 获取订单类型配置信息
	orderTypeConf, ok := config.OrderTypeConfMap[order.OrderType]
	if !ok {
		ctxlog.WithCtx(ctx).Errorf("NewCreateOrder invalid orderTypeConf")
		return errors.IllegalOrderTypeErrorError
	}

	// 写入数据库，初始化当前阶段就为1
	tbOrder := orderdao.TbOrder{
		OrderId:       order.OrderID,
		OrderType:     order.OrderType,
		TotalStageNum: int(orderTypeConf.TotalStageNum),
		Info:          order.Info,
		Exigency:      int(order.Exigency),
		ProposerEmail: order.ProposerEmail,
		OpsOwnerEmail: order.OpsOwnerEmail,
		ApplyMsg:      order.ApplyMsg,
		CurrentStage:  1,
		Ctime:         order.CTime,
	}
	_, err = biz.Dao.OrderMysql.NewOrder(ctx, tbOrder)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewCreateOrder Dao.OrderMysql.NewOrderTaks err=%v", err)
		return
	}
	return
}

// NewHandleOrder 处理工单（通过配置处理所有类型的工单）
func (biz *OrderBiz) NewCreateStage(order *orderdto.Order, ccUserInfos []*orderdto.CcUserInfo) error {
	ctx := context.Background()
	defer func(ctx context.Context) {
		if err := recover(); err != nil {
			ctxlog.WithCtx(ctx).Errorf("NewCreateStage err = %v,but it has been recovered!!!", err)
		}
	}(ctx)

	var currentStage int64 = 1

	// 获取订单类型配置信息
	orderTypeConf, ok := config.OrderTypeConfMap[order.OrderType]
	if !ok {
		ctxlog.WithCtx(ctx).Errorf("CreateStage invalid orderTypeConf")
		return errors.IllegalOrderTypeErrorError
	}

	// 获取审批人员个人、部门信息
	auditPersonInfoMap, err := biz.GetAuditPersonInfoMap(ctx, order)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewCreateStage GetAuditPersonInfoMap err=%v", err)
		err = biz.UpdateOrderStageFail(ctx, order.OrderID, currentStage, err.Error())
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("NewCreateStage GetAuditPersonInfoMap UpdateOrderStageFail err=%v", err)
		}
		return err
	}
	// 初始化订单流程阶段任务
	err = biz.InitOrderStages(ctx, order.OrderID, orderTypeConf, auditPersonInfoMap)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewCreateStage InitOrderStages err=%v", err)
		err = biz.UpdateOrderStageFail(ctx, order.OrderID, currentStage, err.Error())
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("NewCreateStage InitOrderStages UpdateOrderStageFail err=%v", err)
		}
		return err
	}

	// 发送卡片给第一个审批人，后续根据回调逐步审批，最终走到确认逻辑
	err = biz.NewSendFeiShuAudit(ctx, order, auditPersonInfoMap, ccUserInfos)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewCreateStage NewSendFeiShuAudit err=%v", err)
		err = biz.UpdateOrderStageFail(ctx, order.OrderID, currentStage, err.Error())
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("NewCreateStage NewSendFeiShuAudit UpdateOrderStageFail err=%v", err)
		}
		return err
	}

	return nil
}

func (biz *OrderBiz) MakeAuditExecMsgCard(ctx context.Context, orderID string, stageNum int64) (
	msgCard *orderdto.AuditExecMsgCardBaseInfo, err error) {

	order := biz.getAuditExecMsgCardOrderDetails(ctx, orderID)
	auditPersonInfoMap, err := biz.GetAuditPersonInfoMap(ctx, order)
	if err != nil {
		return nil, biz.handleAuditExecError(ctx, orderID, stageNum, err, "MakeAuditExecMsgCard", "GetAuditPersonInfoMap")
	}

	orderTypeConf, ok := config.OrderTypeConfMap[order.OrderType]
	if !ok {
		ctxlog.WithCtx(ctx).Errorf("MakeAuditExecMsgCard OrderTypeConfMap[%s] not value", order.OrderType)
		return nil, errors.IllegalOrderTypeErrorError
	}
	// 获取审批阶段信息
	tbStages, err := biz.Dao.StageMysql.GetStages(ctx, orderID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("MakeAuditExecMsgCard GetStages err=%v", err)
		return nil, err
	}
	// 组装审批人员卡片
	auditPeoples, auditPepoleDetailCardStr, stagesMap := biz.processAuditExecMsgCardStages(tbStages)
	auditPepoleCardStr := fmt.Sprintf(feishuservicedto.NEW_AUDIT_PEPOLE_CARD, auditPepoleDetailCardStr)

	// 组装审批结果卡片
	auditStatusCardStr, auditStatusDetails, auditStatusColors := getAuditExecStatusCard(ctx, tbStages, int(stageNum))

	// 工单发起人信息
	proposerEmail, proposerUserID, proposerDepartmentInfoName := getAuditExecProposerInfo(auditPersonInfoMap)

	// 组装参数
	param := feishuhookdto.AuditHookParam{
		OrderId:                orderID,
		ProposerEmail:          proposerEmail,
		ProposerUserId:         proposerUserID,
		ProposerDepartmentName: proposerDepartmentInfoName,
		OrderTypeNameCN:        orderTypeConf.Name,
		CurrentStage:           int(stageNum),
		AuditPepoles:           auditPeoples,
		AuditResultsCN:         auditStatusDetails,
		AuditResultsColorCN:    auditStatusColors,
	}
	paramJson, err := json.Marshal(param)
	if err != nil {
		return nil, biz.handleAuditExecError(ctx, orderID, stageNum, err, "MakeAuditExecMsgCard", "marshalAuditHookParam")
	}

	stageRecordsMap := createStageRecordsMap(stagesMap)

	msgCard = &orderdto.AuditExecMsgCardBaseInfo{
		ProposerUserID:             proposerUserID,
		ProposerDepartmentInfoName: proposerDepartmentInfoName,
		OrderType:                  orderTypeConf.Name,
		Ctime:                      order.CTime.Format("2006-01-02 15:04"),
		ApplyReason:                order.ApplyMsg,
		AuditPeopleCardStr:         auditPepoleCardStr,
		AuditStatusCardStr:         auditStatusCardStr,
		Param:                      string(paramJson),
		StagesMap:                  stageRecordsMap,
		AuditPersonInfoMap:         auditPersonInfoMap,
	}
	return
}

func (biz *OrderBiz) getAuditExecMsgCardOrderDetails(ctx context.Context,
	orderID string) *orderdto.Order {
	tbOrder, err := biz.Dao.OrderMysql.GetOrder(ctx, orderID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("MakeAuditExecMsgCard GetOrderDetail err=%v", err)
		return nil
	}
	return &orderdto.Order{
		OrderID:       orderID,
		OrderType:     tbOrder.OrderType,
		Info:          tbOrder.Info,
		ApplyMsg:      tbOrder.ApplyMsg,
		ProposerEmail: tbOrder.ProposerEmail,
		OpsOwnerEmail: tbOrder.OpsOwnerEmail,
		CTime:         tbOrder.Ctime,
	}
}

func (biz *OrderBiz) processAuditExecMsgCardStages(tbStages []orderdao.TbStage) (
	[]string, string, map[int]orderdao.TbStage) {
	var auditPeoples []string
	var auditPepoleDetailCards []string
	stagesMap := make(map[int]orderdao.TbStage, 0)

	for _, stage := range tbStages {
		noEmailOperator := strings.ReplaceAll(stage.Operator, "@cmcm.com", "")
		auditPepoleDetailCards = append(auditPepoleDetailCards,
			fmt.Sprintf(feishuservicedto.NEW_AUDIT_PEPOLE_DETAIL_CARD, noEmailOperator))
		auditPeoples = append(auditPeoples, noEmailOperator)
		stagesMap[stage.StageNum] = stage
	}
	auditPepoleDetailCardStr := strings.Join(auditPepoleDetailCards, ",")
	return auditPeoples, auditPepoleDetailCardStr, stagesMap
}

func getAuditExecStatusCard(ctx context.Context, tbStages []orderdao.TbStage, currentStage int) (
	string, []string, []string) {
	auditStatusDetailCards, auditStatusDetails, auditStatusColors := GetAuditStatus(ctx, tbStages, currentStage)
	for i := 0; i < len(auditStatusDetailCards); i++ {
		auditStatusDetailCards[i] = fmt.Sprintf(auditStatusDetailCards[i], auditStatusDetails[i], auditStatusColors[i])
	}
	return fmt.Sprintf(feishuservicedto.NEW_AUDIT_STATUS_CARD,
		strings.Join(auditStatusDetailCards, ",")), auditStatusDetails, auditStatusColors
}

func getAuditExecProposerInfo(auditPersonInfoMap map[string]orderdto.PersonInfo) (
	string, string, string) {
	var proposerEmail, proposerUserID, proposerDepartmentInfoName string
	if proposer, ok := auditPersonInfoMap["self"]; ok {
		proposerEmail = proposer.UserInfo.Email
		proposerUserID = proposer.UserInfo.UserID
		proposerDepartmentInfoName = proposer.DepartmentInfo.Name
	}
	return proposerEmail, proposerUserID, proposerDepartmentInfoName
}

func createStageRecordsMap(stagesMap map[int]orderdao.TbStage) map[int]orderdto.StageRecord {
	stageRecordsMap := make(map[int]orderdto.StageRecord, 0)
	for _, stage := range stagesMap {
		stageRecordsMap[stage.StageNum] = orderdto.StageRecord{
			StageNum:            int64(stage.StageNum),
			StageName:           stage.StageName,
			StageType:           stage.StageType,
			StageOperator:       stage.Operator,
			FeishuOpenId:        stage.FeishuOpenId,
			StageResult:         int64(stage.Result),
			StageResultDesc:     stage.ResultMsg,
			StageResultMsg:      stage.ResultMsg,
			ApplyDatetime:       stage.Ctime.Format("2006-01-02 15:04"),
			LastUpdatedDatetime: stage.Mtime.Format("2006-01-02 15:04"),
			Info:                stage.Info,
			StagePattern:        stage.StagePattern,
		}
	}
	return stageRecordsMap
}

func (biz *OrderBiz) handleAuditExecError(ctx context.Context, orderID string, stageNum int64,
	originalErr error, funcName, methodName string) error {
	ctxlog.WithCtx(ctx).Errorf("%s %s err=%v", funcName, methodName, originalErr)
	if updateErr := biz.UpdateOrderStageFail(ctx, orderID, stageNum, originalErr.Error()); updateErr != nil {
		ctxlog.WithCtx(ctx).Errorf("%s %s UpdateOrderStageFail err=%v", funcName, methodName, updateErr)
		err := golocalerrors.Join(originalErr, updateErr)
		return err
	}
	return originalErr
}

// NewFirstSendFeiShuAudit 首次发送飞书审核卡片与等待回调结果
func (biz *OrderBiz) NewSendFeiShuAudit(ctx context.Context, order *orderdto.Order, auditPersonInfoMap map[string]orderdto.PersonInfo, ccUserInfos []*orderdto.CcUserInfo) (err error) {
	// 获取不同工单的任务流配置信息
	orderTypeConf, ok := config.OrderTypeConfMap[order.OrderType]
	if !ok {
		ctxlog.WithCtx(ctx).Errorf("NewSendFeiShuAudit OrderTypeConfMap[%s] not value", order.OrderType)
		return err
	}

	// 获取审批阶段信息
	tbStages, err := biz.Dao.StageMysql.GetStages(ctx, order.OrderID)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("NewSendFeiShuAudit GetStages err=%v", err)
		return err
	}

	// 各个阶段的map
	var stagesMap map[int]orderdao.TbStage = make(map[int]orderdao.TbStage, 0)

	// 组装审批人员卡片
	var auditPeoples []string = make([]string, 0)
	var auditPepoleDetailCards []string = make([]string, 0)
	for _, stage := range tbStages {
		noEmailOperator := strings.ReplaceAll(stage.Operator, "@cmcm.com", "")
		auditPepoleDetailCards = append(auditPepoleDetailCards, fmt.Sprintf(feishuservicedto.NEW_AUDIT_PEPOLE_DETAIL_CARD, noEmailOperator))
		auditPeoples = append(auditPeoples, noEmailOperator)

		stagesMap[stage.StageNum] = stage
	}
	auditPepoleDetailCardStr := strings.Join(auditPepoleDetailCards, ",")
	auditPepoleCardStr := fmt.Sprintf(feishuservicedto.NEW_AUDIT_PEPOLE_CARD, auditPepoleDetailCardStr)

	// 组装审批结果卡片
	auditStatusDetailCards, auditStatusDetails, auditStatusColors := GetAuditStatus(ctx, tbStages, 1)
	for i := 0; i < len(auditStatusDetailCards); i++ {
		auditStatusDetailCards[i] = fmt.Sprintf(auditStatusDetailCards[i], auditStatusDetails[i], auditStatusColors[i])
	}
	auditStatusDetailCardsStr := strings.Join(auditStatusDetailCards, ",")
	auditStatusCardStr := fmt.Sprintf(feishuservicedto.NEW_AUDIT_STATUS_CARD, auditStatusDetailCardsStr)

	// 工单发起人信息
	var proposerEmail, proposerUserID, proposerDepartmentInfoName string
	if proposer, ok := auditPersonInfoMap["self"]; ok {
		proposerEmail = proposer.UserInfo.Email
		proposerUserID = proposer.UserInfo.UserID
		proposerDepartmentInfoName = proposer.DepartmentInfo.Name
	}

	// 组装参数
	param := feishuhookdto.AuditHookParam{
		OrderId:                order.OrderID,
		ProposerEmail:          proposerEmail,
		ProposerUserId:         proposerUserID,
		ProposerDepartmentName: proposerDepartmentInfoName,
		OrderTypeNameCN:        orderTypeConf.Name,
		CurrentStage:           1,
		AuditPepoles:           auditPeoples,
		AuditResultsCN:         auditStatusDetails,
		AuditResultsColorCN:    auditStatusColors,
	}
	paramJson, err := json.Marshal(param)
	if err != nil {
		return biz.handleAuditExecError(ctx, order.OrderID, 1, err, "NewSendFeiShuAudit", "json.Marshal")
	}

	// 发送消息给第一个审批人
	if personInfo, ok := stagesMap[1]; ok {
		newAuditMsgCard := &feishuservicedto.NewAuditMsgCard{
			ProposerUserID:             proposerUserID,
			ProposerDepartmentInfoName: proposerDepartmentInfoName,
			OrderType:                  orderTypeConf.Name,
			Ctime:                      order.CTime.Format("2006-01-02 15:04"),
			ApplyReason:                order.ApplyMsg,
			AuditPeopleCardStr:         auditPepoleCardStr,
			AuditStatusCardStr:         auditStatusCardStr,
			AgreeButton:                false,
			Param:                      string(paramJson),
			RejectButton:               false,
			OrderTitle:                 "你有一条工单待审批(" + order.OrderID + ")",
			OrderTitleColor:            "blue",
		}
		auditBaseAuditCardStr := feishuservice.GetNewAuditMsgCard(newAuditMsgCard)
		auditMsgID, _, err := biz.FeiShuAudit(ctx, personInfo.FeishuOpenId, auditBaseAuditCardStr)
		if err != nil {
			return biz.handleAuditExecError(ctx, order.OrderID, 1, err, "NewSendFeiShuAudit", "FeiShuAudit")
		}

		auditPattern := orderTypeConf.StagePatterns[0] // 该阶段的审批模式
		stageInfoStr, err := MakeStageInfoJsonByPattern(ctx, auditMsgID, time.Now().Unix(), auditPattern, auditPersonInfoMap)
		if err != nil {
			return biz.handleAuditExecError(ctx, order.OrderID, 1, err, "NewSendFeiShuAudit", "MakeStageInfoJsonByPattern")
		}

		_, err = biz.Dao.StageMysql.UpdateStageInfo(ctx, order.OrderID, int64(1), stageInfoStr)
		if err != nil {
			return biz.handleAuditExecError(ctx, order.OrderID, 1, err, "NewSendFeiShuAudit", "UpdateStageInfo")
		}
	}

	// 发送消息给最后确认人
	if personInfo, ok := stagesMap[int(orderTypeConf.TotalStageNum)]; ok {
		newAuditMsgCard := &feishuservicedto.NewAuditMsgCard{
			ProposerUserID:             proposerUserID,
			ProposerDepartmentInfoName: proposerDepartmentInfoName,
			OrderType:                  orderTypeConf.Name,
			Ctime:                      order.CTime.Format("2006-01-02 15:04"),
			ApplyReason:                order.ApplyMsg,
			AuditPeopleCardStr:         auditPepoleCardStr,
			AuditStatusCardStr:         auditStatusCardStr,
			AgreeButton:                true,
			Param:                      string(paramJson),
			RejectButton:               true,
			OrderTitle:                 "工单(" + order.OrderID + ")执行情况",
			OrderTitleColor:            "blue",
		}
		auditBaseAuditCardStr := feishuservice.GetNewAuditMsgCard(newAuditMsgCard)
		auditMsgID, _, err := biz.FeiShuAudit(ctx, personInfo.FeishuOpenId, auditBaseAuditCardStr)
		if err != nil {
			return biz.handleAuditExecError(ctx, order.OrderID, 1, err, "NewSendFeiShuAudit", "FeiShuAudit")
		}

		auditPattern := orderTypeConf.StagePatterns[orderTypeConf.TotalStageNum-1] // 该阶段的审批模式
		stageInfoStr, err := MakeStageInfoJsonByPattern(ctx, auditMsgID, time.Now().Unix(), auditPattern, auditPersonInfoMap)
		if err != nil {
			return biz.handleAuditExecError(ctx, order.OrderID, 1, err, "NewSendFeiShuAudit", "MakeStageInfoJsonByPattern")
		}

		_, err = biz.Dao.StageMysql.UpdateStageInfo(ctx, order.OrderID, orderTypeConf.TotalStageNum, stageInfoStr)
		if err != nil {
			return biz.handleAuditExecError(ctx, order.OrderID, 1, err, "NewSendFeiShuAudit", "UpdateStageInfo")
		}
	}

	// 向抄送人发送通知
	if len(ccUserInfos) > 0 {
		ctxlog.WithCtx(ctx).Infof("NewSendFeiShuAudit: sending notifications to %d cc users for order %s", len(ccUserInfos), order.OrderID)

		for _, ccUserInfo := range ccUserInfos {
			// 为抄送人构建通知卡片（不含审批按钮）
			ccMsgCard := &feishuservicedto.NewAuditMsgCard{
				ProposerUserID:             order.ProposerEmail, // 使用邮箱作为用户ID
				ProposerDepartmentInfoName: "未知部门",              // 抄送通知中部门信息可以简化
				OrderType:                  order.OrderType,
				Ctime:                      order.CTime.Format("2006-01-02 15:04:05"),
				ApplyReason:                order.ApplyMsg,
				AuditPeopleCardStr:         "", // 抄送通知不需要审批人信息
				AuditStatusCardStr:         auditStatusCardStr,
				AgreeButton:                false, // 抄送人不显示审批按钮
				RejectButton:               false, // 抄送人不显示审批按钮
				OrderTitle:                 "工单(" + order.OrderID + ")抄送通知",
				OrderTitleColor:            "grey",
			}

			ccCardStr := feishuservice.GetNewAuditMsgCard(ccMsgCard)

			// 发送通知给抄送人，实现容错处理
			_, _, err := biz.FeiShuAudit(ctx, ccUserInfo.CcOpenID, ccCardStr)
			if err != nil {
				// 抄送通知失败不影响主流程，只记录错误日志
				ctxlog.WithCtx(ctx).Errorf("NewSendFeiShuAudit: failed to send cc notification to %s (openID: %s) for order %s, error: %v",
					ccUserInfo.CcEmail, ccUserInfo.CcOpenID, order.OrderID, err)
				continue
			}

			ctxlog.WithCtx(ctx).Infof("NewSendFeiShuAudit: successfully sent cc notification to %s for order %s",
				ccUserInfo.CcEmail, order.OrderID)
		}
	}

	return nil
}

func MakeStageInfoJsonByPattern(ctx context.Context, msgID string, sendMsgCardTimeSec int64,
	auditPattern string, auditPersonInfoMap map[string]orderdto.PersonInfo) (stageInfoSting string, err error) {
	auditorOpenID := auditPersonInfoMap[auditPattern].UserInfo.OpenID // 获取审批人飞书openID
	auditorUserID := auditPersonInfoMap[auditPattern].UserInfo.UserID // 获取审批人飞书userID
	auditorEmail := auditPersonInfoMap[auditPattern].UserInfo.Email   // 获取审批人飞书Email
	// 写入飞书消息id到mysql stage表里
	stageInfo := &orderdto.StageInfo{
		FeiShuMsgID:              msgID,
		FeiShuSendMsgCradTimeSec: sendMsgCardTimeSec,
		FeiShuUserEmail:          auditorEmail,
		FeiShuUserOpenID:         auditorOpenID,
		FeiShuUserID:             auditorUserID,
	}
	stageInfoByte, err := json.Marshal(stageInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("MakeStageInfoJsonString Marshal stageInfo err=%v", err)
		return
	}

	stageInfoSting = string(stageInfoByte)
	return stageInfoSting, nil
}

func MakeStageInfoJsonByStr(ctx context.Context, msgID string, sendMsgCardTimeSec int64,
	auditorEmail, auditorOpenID, auditorUserID string) (stageInfoSting string, err error) {
	stageInfo := &orderdto.StageInfo{
		FeiShuMsgID:              msgID,
		FeiShuSendMsgCradTimeSec: sendMsgCardTimeSec,
		FeiShuUserEmail:          auditorEmail,
		FeiShuUserOpenID:         auditorOpenID,
		FeiShuUserID:             auditorUserID,
	}
	stageInfoByte, err := json.Marshal(stageInfo)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("MakeStageInfoJsonByStr Marshal stageInfo err=%v", err)
		return
	}

	stageInfoSting = string(stageInfoByte)
	return stageInfoSting, nil
}

func GetAuditStatus(ctx context.Context, stages []orderdao.TbStage, currentStage int) ([]string, []string, []string) {
	// 组装审批人员审批结果卡片
	var auditStatusDetailCards []string = make([]string, 0)
	var auditStatusDetails []string = make([]string, 0)
	var auditStatusColors []string = make([]string, 0)
	for _, stage := range stages {
		if stage.StageNum > currentStage {
			switch stage.StageType {
			case "audit":
				auditStatusDetails = append(auditStatusDetails, "待审批")
				auditStatusColors = append(auditStatusColors, "grey")
			case "excute":
				auditStatusDetails = append(auditStatusDetails, "待执行")
				auditStatusColors = append(auditStatusColors, "grey")
			case "confirm":
				auditStatusDetails = append(auditStatusDetails, "待确认")
				auditStatusColors = append(auditStatusColors, "grey")
			}
		} else if stage.StageNum == currentStage {
			switch stage.StageType {
			case "audit":
				auditStatusDetails = append(auditStatusDetails, "审批中")
				auditStatusColors = append(auditStatusColors, "orange")
			case "excute":
				auditStatusDetails = append(auditStatusDetails, "执行中")
				auditStatusColors = append(auditStatusColors, "orange")
			case "confirm":
				auditStatusDetails = append(auditStatusDetails, "确认中")
				auditStatusColors = append(auditStatusColors, "orange")
			}

		} else if stage.StageNum < currentStage {
			switch stage.StageType {
			case "audit":
				if stage.Result == 1 {
					auditStatusDetails = append(auditStatusDetails, "同意")
					auditStatusColors = append(auditStatusColors, "green")
				} else {
					auditStatusDetails = append(auditStatusDetails, "驳回")
					auditStatusColors = append(auditStatusColors, "red")
				}
			case "excute":
				if stage.Result == 1 {
					auditStatusDetails = append(auditStatusDetails, "已执行")
					auditStatusColors = append(auditStatusColors, "green")
				} else {
					auditStatusDetails = append(auditStatusDetails, "执行失败")
					auditStatusColors = append(auditStatusColors, "red")
				}
			case "confirm":
				if stage.Result == 1 {
					auditStatusDetails = append(auditStatusDetails, "已验收")
					auditStatusColors = append(auditStatusColors, "green")
				} else {
					auditStatusDetails = append(auditStatusDetails, "未验收")
					auditStatusColors = append(auditStatusColors, "red")

				}
			}
		}
		auditStatusDetailCards = append(auditStatusDetailCards, feishuservicedto.NEW_AUDIT_STATUS_DETAIL_CARD)
	}
	return auditStatusDetailCards, auditStatusDetails, auditStatusColors
}

// HandleWillTimeOutStage 处理没有处理的工单，并让其变为超时状态
func (biz *OrderBiz) HandleWillTimeOutStage(ctx context.Context) {
	now := time.Now()

	endTime := now.Add(-14 * 24 * time.Hour).Add(1 * time.Hour) // 2024-08-10 09:00
	beginTime := now.Add(-14 * 24 * time.Hour)                  // 2024-08-05 09:00

	willTimeOutOrders, err := biz.Dao.OrderMysql.GetLeave3DayNoHandleOrder(ctx, beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("Handle3DayWillTimeOutOrder GetLeave3DayNoHandleOrder err=%v", err)
		return
	}

	// noHandleStages, err := biz.Dao.StageMysql.GetNoHandleStages(ctx, beginTime.Format("2006-01-02 15:04"), endTime.Format("2006-01-02 15:04"))
	// if err != nil {
	// 	ctxlog.WithCtx(ctx).Errorf("HandleWillTimeOutStage GetNoHandleStages err=%v", err)
	// 	return
	// }
	for _, order := range willTimeOutOrders {
		err = biz.UpdateOrderToTimeOutStatus(ctx, order)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleWillTimeOutStage UpdateOrderToTimeOutStatus err=%v", err)
			continue
		}
	}
}

// UpdateOrderToTimeOut 更新工单为超时状态
func (biz *OrderBiz) UpdateOrderToTimeOutStatus(ctx context.Context, order orderdao.TbOrder) error {
	_, err := biz.Dao.StageMysql.UpdateStageResult(ctx, order.OrderId, int64(order.CurrentStage), 3, "timeout")
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage UpdateStageResult err=%v", err)
		return err
	}

	_, err = biz.Dao.OrderMysql.UpdateResult(ctx, order.OrderId, "timeout", 2)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage UpdateResult err=%v", err)
		return err
	}

	stages, err := biz.Dao.StageMysql.GetStages(ctx, order.OrderId)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage GetStages err=%v", err)
		return err
	}

	// 组装人员卡片
	var auditPepoleDetailCards []string = make([]string, 0)
	for _, stage := range stages {
		noEmailOperator := strings.ReplaceAll(stage.Operator, "@cmcm.com", "")
		auditPepoleDetailCards = append(auditPepoleDetailCards, fmt.Sprintf(feishuservicedto.NEW_AUDIT_PEPOLE_DETAIL_CARD, noEmailOperator))
	}
	auditPepoleDetailCardStr := strings.Join(auditPepoleDetailCards, ",")
	auditPepoleCardStr := fmt.Sprintf(feishuservicedto.NEW_AUDIT_PEPOLE_CARD, auditPepoleDetailCardStr)

	// 组装状态卡片
	auditStatusDetailCards, auditStatusDetails, auditStatusColors := GetAuditStatus(ctx, stages, int(order.CurrentStage))
	for i := 0; i < len(auditStatusDetailCards); i++ {
		if i+1 == int(order.CurrentStage) {
			auditStatusDetails[i] = "已超时"
			auditStatusColors[i] = "red"
		}
		auditStatusDetailCards[i] = fmt.Sprintf(auditStatusDetailCards[i], auditStatusDetails[i], auditStatusColors[i])
	}
	auditStatusDetailCardsStr := strings.Join(auditStatusDetailCards, ",")
	auditStatusCardStr := fmt.Sprintf(feishuservicedto.NEW_AUDIT_STATUS_CARD, auditStatusDetailCardsStr)

	// 获取不同工单的任务流配置信息
	orderTypeConf, ok := config.OrderTypeConfMap[order.OrderType]
	if !ok {
		ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage OrderTypeConfMap[%s] not value", order.OrderType)
		return nil
	}

	// 获取工单发起人信息
	proposerInfo, err := biz.GetPersonInfo(ctx, order.ProposerEmail)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage GetPersonInfo err=%v", err)
		return err
	}

	// 更新卡片
	newAuditMsgCard := &feishuservicedto.NewAuditMsgCard{
		ProposerUserID:             proposerInfo.UserInfo.UserID,
		ProposerDepartmentInfoName: proposerInfo.DepartmentInfo.Name,
		OrderType:                  orderTypeConf.Name,
		Ctime:                      order.Ctime.Format("2006-01-02 15:04"),
		ApplyReason:                order.ApplyMsg,
		AuditPeopleCardStr:         auditPepoleCardStr,
		AuditStatusCardStr:         auditStatusCardStr,
		AgreeButton:                true,
		Param:                      "",
		RejectButton:               true,
		OrderTitle:                 "工单(" + order.OrderId + ")执行超时",
		OrderTitleColor:            "red",
	}

	updateCurrentMsg := feishuservice.GetNewAuditMsgCard(newAuditMsgCard)

	var stageMap map[int]orderdao.TbStage = make(map[int]orderdao.TbStage)
	var feiShuOpenID map[string]struct{} = make(map[string]struct{})
	// 发送超时消息
	for _, v := range stages {
		if v.FeishuOpenId == "" {
			continue
		}

		stageInfo := &orderdto.StageInfo{}
		err = json.Unmarshal([]byte(v.Info), stageInfo)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage json.Unmarshal err=%v", err)
			return err
		}
		if int64(v.StageNum) <= int64(order.CurrentStage) && order.TotalStageNum != v.StageNum {
			err = feishuservice.Feishu.UpdateFeishuMessage(ctx, updateCurrentMsg, stageInfo.FeiShuMsgID)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage UpdateFeishuMessage err=%v", err)
				return err
			}
		}

		if int64(v.StageNum) == int64(order.TotalStageNum) {
			err = feishuservice.Feishu.UpdateFeishuMessage(ctx, updateCurrentMsg, stageInfo.FeiShuMsgID)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("HandleConfirmStage UpdateFeishuMessage err=%v", err)
				return err
			}
		}

		feiShuOpenID[v.FeishuOpenId] = struct{}{}

		stageMap[v.StageNum] = v
	}

	for k := range feiShuOpenID {
		// 发送工单顺利完结消息卡片
		orderTimeOutMsgCard := feishuservice.GetOrderTimeoutMsgCard(order.OrderId)
		msgId, err := feishuservice.Feishu.SendMsgByOpenID(ctx, k, orderTimeOutMsgCard)
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("NewHandleMsgCardCallBack SendMsgByOpenID  msgID=%s err=%v", msgId, err)
		}
	}

	// 如果是sql工单则需要入一份到sqlaudit中
	go sqlauditbiz.GetInstance().InsertWfoStageToSqlAudit(orderTypeConf.Name, order.OrderId, order.Info,
		stageMap[order.CurrentStage].StageName, stageMap[order.CurrentStage].StageType, stageMap[order.CurrentStage].StageNum,
		stageMap[order.CurrentStage].StagePattern, stageMap[order.CurrentStage].StagePattern, 3, "timeout")

	return nil
}

// Handle3DayWillTimeOutOrder 处理即将过期的工单
func (biz *OrderBiz) Handle3DayWillTimeOutOrder(ctx context.Context) {
	now := time.Now()                          // 2024-08-19 09:00
	endTime := now.Add(-10 * 24 * time.Hour)   // 2024-08-10 09:00
	beginTime := now.Add(-14 * 24 * time.Hour) // 2024-08-05 09:00

	willTimeOutOrders, err := biz.Dao.OrderMysql.GetLeave3DayNoHandleOrder(ctx, beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("Handle3DayWillTimeOutOrder GetLeave3DayNoHandleOrder err=%v", err)
		return
	}

	for _, order := range willTimeOutOrders {
		stage, err := biz.Dao.StageMysql.GetStage(ctx, order.OrderId, int64(order.CurrentStage))
		if err != nil {
			ctxlog.WithCtx(ctx).Errorf("Handle3DayWillTimeOutOrder GetStage err=%v", err)
			return
		}

		// 发送工单提醒
		willTimeOutMsgCard := fmt.Sprintf(feishuservicedto.ORDER_WILL_EXPIRE_MSG_CARD, order.OrderId)
		if stage.FeishuOpenId != "" {
			_, err = feishuservice.Feishu.SendMsgByOpenID(ctx, stage.FeishuOpenId, willTimeOutMsgCard)
			if err != nil {
				ctxlog.WithCtx(ctx).Errorf("Handle3DayWillTimeOutOrder SendMsgByOpenID FeishuOpenId=%s,err=%v", stage.FeishuOpenId, err)
				return
			}
		}
	}
}

// ValidateUserAndBuildInfo 根据邮箱查询飞书用户，并组装其姓名、部门和OpenID信息
func (biz *OrderBiz) ValidateUserAndBuildInfo(ctx context.Context, email string) (*orderdto.ValidatedUserInfo, error) {
	// 调用飞书API获取用户信息
	userInfo, err := feishuservice.Feishu.GetUserInfoByEmail(ctx, email)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("ValidateUserAndBuildInfo GetUserInfoByEmail failed: %v", err)
		return nil, fmt.Errorf("该邮箱不存在或非内部员工，请检查")
	}

	// 检查用户是否有部门信息
	if len(userInfo.DepartmentIDS) == 0 {
		ctxlog.WithCtx(ctx).Errorf("ValidateUserAndBuildInfo user %s has no department", email)
		return nil, fmt.Errorf("用户部门信息缺失")
	}

	// 获取部门信息
	departmentInfo, err := feishuservice.Feishu.GetDepartmentInfo(ctx, userInfo.DepartmentIDS[0])
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("ValidateUserAndBuildInfo GetDepartmentInfo failed: %v", err)
		return nil, fmt.Errorf("获取部门信息失败")
	}

	// 组装返回信息
	validatedUserInfo := &orderdto.ValidatedUserInfo{
		Name:       userInfo.Name,
		Department: departmentInfo.Name,
		OpenID:     userInfo.OpenID,
	}

	return validatedUserInfo, nil
}

// HandleOrderCc 处理工单抄送逻辑
func (biz *OrderBiz) HandleOrderCc(ctx context.Context, orderID string, ccUserInfos []*orderdto.CcUserInfo) error {
	if len(ccUserInfos) == 0 {
		return nil
	}

	// 转换为DAO层的数据结构
	ccRecords := make([]*orderdao.OrderCc, 0, len(ccUserInfos))
	for _, ccInfo := range ccUserInfos {
		ccRecord := &orderdao.OrderCc{
			OrderId:  orderID,
			CcOpenId: ccInfo.CcOpenID,
			CcEmail:  ccInfo.CcEmail,
		}
		ccRecords = append(ccRecords, ccRecord)
	}

	// 批量插入抄送记录
	err := biz.Dao.BatchNewCcRecords(ctx, ccRecords)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("HandleOrderCc BatchNewCcRecords failed: %v", err)
		return fmt.Errorf("保存抄送记录失败")
	}

	ctxlog.WithCtx(ctx).Infof("HandleOrderCc success for order: %s, cc count: %d", orderID, len(ccUserInfos))
	return nil
}

// SendCcNotification 发送抄送通知（迭代二再实现）
func (biz *OrderBiz) SendCcNotification(ctx context.Context, orderID string, ccUserInfos []*orderdto.CcUserInfo) error {
	// TODO: 迭代二再实现
	// 未来实现逻辑:
	// 1. 获取工单详细信息
	// 2. 构建抄送通知消息
	// 3. 通过飞书API发送通知给抄送人
	// 4. 记录通知发送状态

	ctxlog.WithCtx(ctx).Infof("SendCcNotification placeholder called for order: %s, cc count: %d", orderID, len(ccUserInfos))

	// 当前迭代不发送通知
	return nil
}

// convertOrderWithRoleToOrderInfo 转换OrderWithRoleInfo为OrderInfo
func (biz *OrderBiz) convertOrderWithRoleToOrderInfo(ordersWithRole []orderdao.OrderWithRoleInfo) []orderdto.OrderInfo {
	var orderInfos []orderdto.OrderInfo

	for _, orderWithRole := range ordersWithRole {
		orderInfo := orderdto.OrderInfo{
			OrderID:             orderWithRole.OrderId,
			OrderType:           orderWithRole.OrderType,
			OrderTypeName:       config.OrderTypeConfMap[orderWithRole.OrderType].Name,
			TotalStageNum:       int64(orderWithRole.TotalStageNum),
			CurrentStage:        int64(orderWithRole.CurrentStage),
			CurrentStageName:    orderWithRole.StageName,
			Result:              int64(orderWithRole.Result),
			ResultDesc:          orderdto.OrderResults[orderWithRole.Result],
			ProposerEmail:       orderWithRole.ProposerEmail,
			OpsOwnerEmail:       orderWithRole.OpsOwnerEmail,
			ApplyMsg:            orderWithRole.ApplyMsg,
			Info:                orderWithRole.Info,
			ApplyDatetime:       orderWithRole.Ctime.Format(aixtime.DefFmt),
			LastUpdatedDatetime: orderWithRole.Mtime.Format(aixtime.DefFmt),
			IsCc:                orderWithRole.IsCc,
			ApprovalStatusForMe: orderWithRole.ApprovalStatusForMe,
		}

		orderInfos = append(orderInfos, orderInfo)
	}

	return orderInfos
}
