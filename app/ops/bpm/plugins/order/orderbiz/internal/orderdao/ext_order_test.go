package orderdao

import (
	"testing"
)

// TestBuildAuditRoleFilterClause 测试审批工单角色筛选条件构建
func TestBuildAuditRoleFilterClause(t *testing.T) {
	dao := &OrderDao{}

	tests := []struct {
		name           string
		filterByRole   []string
		expectedClause string
		expectedArgs   int
	}{
		{
			name:           "空筛选条件",
			filterByRole:   []string{},
			expectedClause: "AND (order_combined.max_audit_priority > 0 OR order_combined.is_cc_to_me = 1)",
			expectedArgs:   0,
		},
		{
			name:           "待我审批",
			filterByRole:   []string{"TO_BE_APPROVED"},
			expectedClause: "AND (order_combined.max_audit_priority = 3)",
			expectedArgs:   0,
		},
		{
			name:           "我已审批",
			filterByRole:   []string{"ALREADY_APPROVED"},
			expectedClause: "AND ((order_combined.max_audit_priority = 1 OR order_combined.max_audit_priority = 2))",
			expectedArgs:   0,
		},
		{
			name:           "抄送我的",
			filterByRole:   []string{"CC_TO_ME"},
			expectedClause: "AND (order_combined.is_cc_to_me = 1)",
			expectedArgs:   0,
		},
		{
			name:           "多个条件",
			filterByRole:   []string{"TO_BE_APPROVED", "CC_TO_ME"},
			expectedClause: "AND (order_combined.max_audit_priority = 3 OR order_combined.is_cc_to_me = 1)",
			expectedArgs:   0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clause, args := dao.buildAuditRoleFilterClause(tt.filterByRole)
			if clause != tt.expectedClause {
				t.Errorf("buildAuditRoleFilterClause() clause = %v, want %v", clause, tt.expectedClause)
			}
			if len(args) != tt.expectedArgs {
				t.Errorf("buildAuditRoleFilterClause() args length = %v, want %v", len(args), tt.expectedArgs)
			}
		})
	}
}

// TestBuildOrderRoleFilterClause 测试进行中/已完结工单角色筛选条件构建
func TestBuildOrderRoleFilterClause(t *testing.T) {
	dao := &OrderDao{}
	userEmail := "<EMAIL>"

	tests := []struct {
		name           string
		filterByRole   []string
		expectedClause string
		expectedArgs   int
	}{
		{
			name:           "空筛选条件",
			filterByRole:   []string{},
			expectedClause: "AND (o.proposer_email = ? OR occ.cc_email IS NOT NULL)",
			expectedArgs:   1,
		},
		{
			name:           "我申请的",
			filterByRole:   []string{"APPLICANT"},
			expectedClause: "AND (o.proposer_email = ?)",
			expectedArgs:   1,
		},
		{
			name:           "抄送我的",
			filterByRole:   []string{"CC_TO_ME"},
			expectedClause: "AND (occ.cc_email IS NOT NULL)",
			expectedArgs:   0,
		},
		{
			name:           "多个条件",
			filterByRole:   []string{"APPLICANT", "CC_TO_ME"},
			expectedClause: "AND (o.proposer_email = ? OR occ.cc_email IS NOT NULL)",
			expectedArgs:   1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clause, args := dao.buildOrderRoleFilterClause(tt.filterByRole, userEmail)
			if clause != tt.expectedClause {
				t.Errorf("buildOrderRoleFilterClause() clause = %v, want %v", clause, tt.expectedClause)
			}
			if len(args) != tt.expectedArgs {
				t.Errorf("buildOrderRoleFilterClause() args length = %v, want %v", len(args), tt.expectedArgs)
			}
		})
	}
}
