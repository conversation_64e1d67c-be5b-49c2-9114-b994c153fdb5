package orderdao

import (
	"time"
)

type SelectCount struct {
	TotalNum int64 `json:"total_num" yaml:"total_num" db:"total_num"`
}

type TbCurrentOrderStageInfo struct {
	OrderID       string    `json:"order_id" yaml:"order_id" db:"order_id"`
	OrderType     string    `json:"order_type" yaml:"order_type" db:"order_type"`
	TotalStageNum int64     `json:"total_stage_num" yaml:"total_stage_num" db:"total_stage_num"`
	CurrentStage  int64     `json:"current_stage" yaml:"current_stage" db:"current_stage"`
	StageName     string    `json:"stage_name" yaml:"stage_name" db:"stage_name"`
	Operator      string    `json:"operator" yaml:"operator" db:"operator"`
	ProposerEmail string    `json:"proposer_email" yaml:"proposer_email" db:"proposer_email"`
	OpsOwnerEmail string    `json:"ops_owner_email" yaml:"ops_owner_email" db:"ops_owner_email"`
	Result        int64     `json:"result" yaml:"result" db:"result"`
	Ctime         time.Time `json:"ctime" yaml:"ctime" db:"ctime"`
	Mtime         time.Time `json:"mtime" yaml:"mtime" db:"mtime"`
}

// MyAuditOrderSearchParam 我的审批工单搜索参数
// 用于支持工单列表的动态查询功能，包含分页、筛选和搜索条件
type MyAuditOrderSearchParam struct {
	PageNum          int      `json:"page_num"`           // 页码，从1开始
	PageSize         int      `json:"page_size"`          // 每页数量
	OrderId          string   `json:"order_id"`           // 工单号（模糊搜索）
	Title            string   `json:"title"`              // 工单标题（模糊搜索，在info的JSON字段中搜索）
	AppliedStartDate *string  `json:"applied_start_date"` // 申请开始时间（ISO 8601格式）
	AppliedEndDate   *string  `json:"applied_end_date"`   // 申请结束时间（ISO 8601格式）
	OrderTypes       []string `json:"order_types"`        // 工单类型数组
	OperatorEmail    string   `json:"operator_email"`     // 审批人邮箱（必填）
}

// TbOrderExtended 扩展的工单结构体
type TbOrderExtended struct {
	Id            int       `json:"id" yaml:"id" db:"id"`
	OrderId       string    `json:"order_id" yaml:"order_id" db:"order_id"`
	OrderType     string    `json:"order_type" yaml:"order_type" db:"order_type"`
	Info          string    `json:"info" yaml:"info" db:"info"`
	Exigency      int       `json:"exigency" yaml:"exigency" db:"exigency"`
	ApplyMsg      string    `json:"apply_msg" yaml:"apply_msg" db:"apply_msg"`
	ProposerEmail string    `json:"proposer_email" yaml:"proposer_email" db:"proposer_email"`
	OpsOwnerEmail string    `json:"ops_owner_email" yaml:"ops_owner_email" db:"ops_owner_email"`
	TotalStageNum int       `json:"total_stage_num" yaml:"total_stage_num" db:"total_stage_num"`
	CurrentStage  int       `json:"current_stage" yaml:"current_stage" db:"current_stage"`
	Result        int       `json:"result" yaml:"result" db:"result"`
	ResultMsg     string    `json:"result_msg" yaml:"result_msg" db:"result_msg"`
	IsDel         int       `json:"is_del" yaml:"is_del" db:"is_del"`
	Ctime         time.Time `json:"ctime" yaml:"ctime" db:"ctime"`
	Mtime         time.Time `json:"mtime" yaml:"mtime" db:"mtime"`
}

func (m *OrderCc) TableName() string {
	return "tb_order_cc"
}

// GetMyOrdersDAOParam 用于列表查询的参数结构体（为迭代三预留）
type GetMyOrdersDAOParam struct {
	UserID       string   `json:"user_id"`
	Page         int      `json:"page"`
	PageSize     int      `json:"page_size"`
	FilterByRole []string `json:"filter_by_role"` // 迭代三再实现：按角色筛选
}

// OrderWithRoleInfo 连表查询结果结构体，包含工单信息和角色信息
type OrderWithRoleInfo struct {
	// 工单基本信息
	Id            int       `json:"id" yaml:"id" db:"id"`
	OrderId       string    `json:"order_id" yaml:"order_id" db:"order_id"`
	OrderType     string    `json:"order_type" yaml:"order_type" db:"order_type"`
	Info          string    `json:"info" yaml:"info" db:"info"`
	Exigency      int       `json:"exigency" yaml:"exigency" db:"exigency"`
	ApplyMsg      string    `json:"apply_msg" yaml:"apply_msg" db:"apply_msg"`
	ProposerEmail string    `json:"proposer_email" yaml:"proposer_email" db:"proposer_email"`
	OpsOwnerEmail string    `json:"ops_owner_email" yaml:"ops_owner_email" db:"ops_owner_email"`
	TotalStageNum int       `json:"total_stage_num" yaml:"total_stage_num" db:"total_stage_num"`
	CurrentStage  int       `json:"current_stage" yaml:"current_stage" db:"current_stage"`
	Result        int       `json:"result" yaml:"result" db:"result"`
	ResultMsg     string    `json:"result_msg" yaml:"result_msg" db:"result_msg"`
	IsDel         int       `json:"is_del" yaml:"is_del" db:"is_del"`
	Ctime         time.Time `json:"ctime" yaml:"ctime" db:"ctime"`
	Mtime         time.Time `json:"mtime" yaml:"mtime" db:"mtime"`

	// 角色信息
	IsApplicant       bool `json:"is_applicant" yaml:"is_applicant" db:"is_applicant"`                      // 是否为申请人
	IsToBeApproved    bool `json:"is_to_be_approved" yaml:"is_to_be_approved" db:"is_to_be_approved"`       // 是否为待审批人
	IsAlreadyApproved bool `json:"is_already_approved" yaml:"is_already_approved" db:"is_already_approved"` // 是否为已审批人
	IsCcToMe          bool `json:"is_cc_to_me" yaml:"is_cc_to_me" db:"is_cc_to_me"`                         // 是否为抄送人

	IsCc int `json:"is_cc" yaml:"is_cc" db:"is_cc"` // 抄送状态 (0/1)

	// 新增字段：审批工单专用
	// 审批状态优先级 (0-无关联, 1-已审, 2-将审, 3-待审)
	ApprovalStatusForMe int `json:"approval_status_for_me" yaml:"approval_status_for_me" db:"approval_status_for_me"`

	// 新增字段：进行中工单专用
	StageName string `json:"stage_name" yaml:"stage_name" db:"stage_name"` // 当前阶段名称
}

// MyOrdersDAOParam DAO层查询参数
type MyOrdersDAOParam struct {
	UserEmail    string   `json:"user_email"`
	PageNum      int      `json:"page_num"`
	PageSize     int      `json:"page_size"`
	FilterByRole []string `json:"filter_by_role"` // 按角色筛选（迭代三预留）
}

// MyAuditOrdersDAOParam 我的审批工单DAO层查询参数
type MyAuditOrdersDAOParam struct {
	PageNum          int      `json:"page_num"`
	PageSize         int      `json:"page_size"`
	OrderId          string   `json:"order_id"`           // 工单号（模糊搜索）
	Title            string   `json:"title"`              // 工单标题（模糊搜索）
	AppliedStartDate *string  `json:"applied_start_date"` // 申请开始时间
	AppliedEndDate   *string  `json:"applied_end_date"`   // 申请结束时间
	OrderTypes       []string `json:"order_types"`        // 工单类型数组
	OperatorEmail    string   `json:"operator_email"`     // 操作者邮箱
	FilterByRole     []string `json:"filter_by_role"`     // 按角色筛选（迭代三预留）
}
