package orderdao

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aix.zhhainiao.com/aixpublic/server/core/ctxlog"
	"aix.zhhainiao.com/opspublic/utils/aixdao"
	"github.com/jmoiron/sqlx"
)

// PagedQuery 分页查询的公共方法 (泛型版本)
// T 必须是切片类型, 如 []SomeStruct
func PagedQuery[T any](ctx context.Context, tx interface{}, querySQL string, baseArgs []interface{}, pageNum, pageSize int) (
	resultList T, err error) {
	// 处理分页参数
	queryArgs := make([]interface{}, len(baseArgs))
	copy(queryArgs, baseArgs) // 复制基础搜索条件参数

	var pageSQL string
	if pageSize > 0 {
		pageSQL = "LIMIT ?,?"
		offset := (pageNum - 1) * pageSize
		limit := pageSize
		queryArgs = append(queryArgs, offset, limit)
	}

	// 构建完整的SQL语句
	realQuerySQL := strings.Replace(querySQL, "%s", pageSQL, 1)
	finalSQL, finalArgs, err := sqlx.In(realQuerySQL, queryArgs...)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("sqlx.In query err=%s,sql:%s,args:%#v", err, realQuerySQL, queryArgs)
		return
	}

	// 为数据查询设置超时时间
	queryCtx, cancel := context.WithTimeout(ctx, 45*time.Second)
	defer cancel()

	// 根据传入的tx类型进行类型断言并执行查询
	if queryer, ok := tx.(sqlx.QueryerContext); ok {
		// 直接使用 resultList 的地址, 不需要中间变量
		err = sqlx.SelectContext(queryCtx, queryer, &resultList, finalSQL, finalArgs...)
	} else {
		err = fmt.Errorf("invalid tx type")
		return
	}

	if err != nil {
		if err == context.DeadlineExceeded {
			ctxlog.WithCtx(ctx).Errorf("PagedQuery timeout after 45s")
		} else {
			ctxlog.WithCtx(ctx).Errorf("PagedQuery err=%s", err)
		}
		return
	}

	return
}

// Count 查询总数的公共方法
func (dao *OrderDao) Count(ctx context.Context, tx interface{}, countSQL string, args []interface{}) (total int, err error) {
	countResource := make([]aixdao.SelectCountResult, 0)

	// 根据传入的tx类型进行类型断言
	if queryer, ok := tx.(sqlx.QueryerContext); ok {
		err = sqlx.SelectContext(ctx, queryer, &countResource, countSQL, args...)
	} else {
		err = fmt.Errorf("invalid tx type")
		return
	}

	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("Count err=%+v,sqlTotal=%s", err, countSQL)
		return
	}
	if len(countResource) > 0 {
		total = countResource[0].Total
	} else {
		err = fmt.Errorf("ext sql count zero err")
		return
	}
	return
}

// GetMyDoingOrdersWithRole 获取用户的进行中工单及角色信息
func (dao *OrderDao) GetMyDoingOrdersWithRole(ctx context.Context, param *MyOrdersDAOParam) ([]OrderWithRoleInfo, int64, error) {
	tx := dao.OrderMysql.getDb()

	// 提取工单状态过滤条件
	doingOrdersFilter := "o.result = 0" // 进行中工单

	// 构建角色筛选条件
	roleFilterClause, roleFilterArgs := dao.buildOrderRoleFilterClause(param.FilterByRole, param.UserEmail)

	// 构建查询进行中工单的SQL
	querySQL := `
		SELECT
			o.id, o.order_id, o.order_type, o.info, o.exigency, o.apply_msg,
			o.proposer_email, o.ops_owner_email, o.total_stage_num, o.current_stage,
			o.result, o.result_msg, o.is_del, o.ctime, o.mtime,
			COALESCE(s.stage_name, "") AS stage_name, -- 当前阶段名称
			-- 判断是否是抄送给当前用户的
			CASE WHEN occ.cc_email IS NOT NULL THEN 1 ELSE 0 END AS is_cc
		FROM tb_order AS o
		LEFT JOIN -- 连接 tb_stage, ON 条件限定 order_id 和 current_stage 的匹配
			tb_stage AS s ON o.order_id = s.order_id
						 AND s.stage_num = o.current_stage
		LEFT JOIN -- 连接 tb_order_cc, ON 条件限定 order_id 和 cc_email 的匹配
			tb_order_cc AS occ ON o.order_id = occ.order_id
						      AND occ.cc_email = ? -- 参数1: 我的邮箱 (用于判断抄送我的)
		WHERE
			o.is_del = 0
			AND ` + doingOrdersFilter + ` -- 进行中工单 (doingOrdersFilter 的内容)
			` + roleFilterClause + `
		ORDER BY
			o.ctime DESC
		%s -- 占位符用于 LIMIT/OFFSET, 在应用层拼接
	`

	countSQL := `
		SELECT COUNT(o.id) AS total
		FROM tb_order AS o
		LEFT JOIN -- 连接 tb_stage, ON 条件限定 order_id 和 current_stage 的匹配
			tb_stage AS s ON o.order_id = s.order_id
						 AND s.stage_num = o.current_stage
		LEFT JOIN -- 连接 tb_order_cc, ON 条件限定 order_id 和 cc_email 的匹配
			tb_order_cc AS occ ON o.order_id = occ.order_id
						      AND occ.cc_email = ? -- 参数1: 我的邮箱 (用于判断抄送我的)
		WHERE
			o.is_del = 0
			AND ` + doingOrdersFilter + ` -- 进行中工单
			` + roleFilterClause + `
	`

	// 查询参数 (按照模板中的参数顺序)
	queryArgs := []interface{}{param.UserEmail} // 参数1: 用于判断抄送我的
	queryArgs = append(queryArgs, roleFilterArgs...)

	// 计数参数 (按照countSQL中的参数顺序)
	countArgs := []interface{}{param.UserEmail} // 参数1: 用于判断抄送我的
	countArgs = append(countArgs, roleFilterArgs...)

	// 执行计数查询
	totalInt, err := dao.Count(ctx, tx, countSQL, countArgs)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyDoingOrdersWithRole count query failed: %v", err)
		return nil, 0, err
	}
	total := int64(totalInt)

	// 执行分页查询 - 直接返回值类型切片
	orders, err := PagedQuery[[]OrderWithRoleInfo](ctx, tx, querySQL, queryArgs, param.PageNum, param.PageSize)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyDoingOrdersWithRole data query failed: %v", err)
		return nil, 0, err
	}

	ctxlog.WithCtx(ctx).Infof("GetMyDoingOrdersWithRole success, found %d doing orders (total: %d) for user %s", len(orders), total, param.UserEmail)
	return orders, total, nil
}

// GetMyDoneOrdersWithRole 获取用户的已完结工单及角色信息
func (dao *OrderDao) GetMyDoneOrdersWithRole(ctx context.Context, param *MyOrdersDAOParam) ([]OrderWithRoleInfo, int64, error) {
	tx := dao.OrderMysql.getDb()

	// 提取工单状态过滤条件
	doneOrdersFilter := "o.result != 0" // 已完结工单

	// 构建角色筛选条件
	roleFilterClause, roleFilterArgs := dao.buildOrderRoleFilterClause(param.FilterByRole, param.UserEmail)

	// 构建查询已完结工单的SQL - 严格按照模板
	querySQL := `
		SELECT DISTINCT
			o.id, o.order_id, o.order_type, o.info, o.exigency, o.apply_msg,
			o.proposer_email, o.ops_owner_email, o.total_stage_num, o.current_stage,
			o.result, o.result_msg, o.is_del, o.ctime, o.mtime,
			-- 判断工单是否抄送给当前用户
			CASE WHEN occ.cc_email IS NOT NULL THEN 1 ELSE 0 END AS is_cc
		FROM tb_order AS o
		LEFT JOIN -- 连接 tb_order_cc 表，筛选抄送给当前用户的记录
			tb_order_cc AS occ ON o.order_id = occ.order_id
							  AND occ.cc_email = ? -- 参数1: 当前用户的邮箱
		WHERE
			o.is_del = 0       -- 确保工单未被删除
			AND ` + doneOrdersFilter + ` -- 已完结工单
			` + roleFilterClause + `
		ORDER BY
			o.ctime DESC -- 按创建时间倒序排列，最新工单在前
		%s -- LIMIT/OFFSET 的占位符
	`

	countSQL := `
		SELECT COUNT(DISTINCT o.id) AS total
		FROM tb_order AS o
		LEFT JOIN -- 连接 tb_order_cc 表，筛选抄送给当前用户的记录
			tb_order_cc AS occ ON o.order_id = occ.order_id
							  AND occ.cc_email = ? -- 参数1: 当前用户的邮箱
		WHERE
			o.is_del = 0       -- 确保工单未被删除
			AND ` + doneOrdersFilter + ` -- 已完结工单
			` + roleFilterClause + `
	`

	// 查询参数 (按照模板中的参数顺序)
	queryArgs := []interface{}{param.UserEmail} // 参数1: 用于LEFT JOIN的邮箱
	queryArgs = append(queryArgs, roleFilterArgs...)

	// 计数参数 (按照countSQL中的参数顺序)
	countArgs := []interface{}{param.UserEmail} // 参数1: 用于LEFT JOIN的邮箱
	countArgs = append(countArgs, roleFilterArgs...)

	// 执行计数查询
	totalInt, err := dao.Count(ctx, tx, countSQL, countArgs)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyDoneOrdersWithRole count query failed: %v", err)
		return nil, 0, err
	}
	total := int64(totalInt)

	// 执行分页查询 - 直接返回值类型切片
	orders, err := PagedQuery[[]OrderWithRoleInfo](ctx, tx, querySQL, queryArgs, param.PageNum, param.PageSize)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyDoneOrdersWithRole data query failed: %v", err)
		return nil, 0, err
	}

	ctxlog.WithCtx(ctx).Infof("GetMyDoneOrdersWithRole success, found %d done orders (total: %d) for user %s", len(orders), total, param.UserEmail)
	return orders, total, nil
}

// getMyAuditOrdersWithRoleConditions 构建审批工单查询的WHERE条件
func (dao *OrderDao) getMyAuditOrdersWithRoleConditions(param *MyAuditOrdersDAOParam) (string, []interface{}, []interface{}) {
	var conditions []string
	var queryArgs []interface{}
	var countArgs []interface{}

	// 添加基础查询条件
	conditions = append(conditions, "o.is_del = 0")

	// 单号
	if param.OrderId != "" {
		conditions = append(conditions, "o.order_id LIKE ?")
		queryArgs = append(queryArgs, "%"+param.OrderId+"%")
		countArgs = append(countArgs, "%"+param.OrderId+"%")
	}

	// 标题
	if param.Title != "" {
		// 对LIKE查询中的特殊字符进行转义, 避免 SQL LIKE 特殊字符干扰
		escapedTitle := strings.ReplaceAll(param.Title, "\\", "\\\\")
		escapedTitle = strings.ReplaceAll(escapedTitle, "%", "\\%")
		escapedTitle = strings.ReplaceAll(escapedTitle, "_", "\\_")

		titleCondition := `(
			JSON_VALID(o.info) = 1 
			AND JSON_UNQUOTE(JSON_EXTRACT(o.info, '$.title')) IS NOT NULL 
			AND JSON_UNQUOTE(JSON_EXTRACT(o.info, '$.title')) LIKE ? ESCAPE '\\'
		)`
		conditions = append(conditions, titleCondition)
		queryArgs = append(queryArgs, "%"+escapedTitle+"%")
		countArgs = append(countArgs, "%"+escapedTitle+"%")
	}

	// 申请时间
	if param.AppliedStartDate != nil && *param.AppliedStartDate != "" {
		conditions = append(conditions, "o.ctime >= ?")
		queryArgs = append(queryArgs, *param.AppliedStartDate)
		countArgs = append(countArgs, *param.AppliedStartDate)
	}

	if param.AppliedEndDate != nil && *param.AppliedEndDate != "" {
		conditions = append(conditions, "o.ctime <= ?")
		queryArgs = append(queryArgs, *param.AppliedEndDate)
		countArgs = append(countArgs, *param.AppliedEndDate)
	}

	// 工单类型
	if len(param.OrderTypes) > 0 {
		placeholders := make([]string, len(param.OrderTypes))
		for i := range param.OrderTypes {
			placeholders[i] = "?"
		}
		conditions = append(conditions, "o.order_type IN ("+strings.Join(placeholders, ",")+")")
		for _, orderType := range param.OrderTypes {
			queryArgs = append(queryArgs, orderType)
			countArgs = append(countArgs, orderType)
		}
	}

	// 构建WHERE子句
	whereClause := "WHERE 1=1"
	if len(conditions) > 0 {
		whereClause += " AND " + strings.Join(conditions, " AND ")
	}

	return whereClause, queryArgs, countArgs
}

// GetMyAuditOrdersWithRole 获取用户的审批工单及角色信息
func (dao *OrderDao) GetMyAuditOrdersWithRole(ctx context.Context, param *MyAuditOrdersDAOParam) ([]OrderWithRoleInfo, int64, error) {
	tx := dao.OrderMysql.getDb()

	// 构建搜索条件
	whereClause, queryArgs, countArgs := dao.getMyAuditOrdersWithRoleConditions(param)

	// 构建公共的子查询部分
	commonSubQuery := `
		LEFT JOIN (
			-- OrderCombinedStatus 的逻辑 (保持不变)
			SELECT
				o_inner.order_id,
				MAX(COALESCE(mas.audit_priority_level, 0)) AS max_audit_priority,
				MAX(CASE WHEN occ.cc_email IS NOT NULL THEN 1 ELSE 0 END) AS is_cc_to_me
			FROM
				tb_order AS o_inner
			LEFT JOIN (
				-- MyAuditStages 的逻辑 (保持不变)
				SELECT
					s.order_id,
					s.stage_num,
					CASE
						WHEN s.stage_num = o_deep_inner.current_stage THEN 3
						WHEN s.stage_num > o_deep_inner.current_stage THEN 2
						WHEN s.stage_num < o_deep_inner.current_stage THEN 1
						ELSE 0
					END AS audit_priority_level
				FROM
					tb_stage AS s
				JOIN
					tb_order AS o_deep_inner ON s.order_id = o_deep_inner.order_id
				WHERE
					s.operator = ? -- 参数1: your_email
			) AS mas ON o_inner.order_id = mas.order_id
			LEFT JOIN
				tb_order_cc AS occ ON o_inner.order_id = occ.order_id
								  AND occ.cc_email = ? -- 参数2: your_email
			GROUP BY
				o_inner.order_id
		) AS order_combined ON o.order_id = order_combined.order_id
	`

	// 构建角色筛选条件
	roleFilterClause, roleFilterArgs := dao.buildAuditRoleFilterClause(param.FilterByRole)

	// 构建查询SQL
	baseSQL := `
		SELECT
			o.id, o.order_id, o.order_type, o.info, o.exigency, o.apply_msg,
			o.proposer_email, o.ops_owner_email, o.total_stage_num, o.current_stage,
			o.result, o.result_msg, o.is_del, o.ctime, o.mtime,
			order_combined.is_cc_to_me AS is_cc,
			-- 基于工单的 result 字段, 调整最终的审批状态
			CASE
				WHEN o.result != 0 THEN 0
				ELSE -- 如果工单是进行中 (result = 0)
					order_combined.max_audit_priority -- 保持原有的审批优先级
			END AS approval_status_for_me
		FROM
			tb_order AS o
		` + commonSubQuery + `
		` + whereClause + `
		` + roleFilterClause + `
		ORDER BY
			-- 可以根据需求调整排序, 例如：进行中优先, 然后是优先级, 最后是时间
			CASE WHEN o.result = 0 THEN 0 ELSE 1 END, -- 进行中的排在前面
			CASE WHEN o.result = 0 THEN order_combined.max_audit_priority ELSE 0 END DESC, -- 进行中工单按优先级降序
			o.ctime DESC -- 最后按时间降序
		%s
	`

	// 构建计数SQL - 严格按照模板结构修改
	countSQL := `
		SELECT COUNT(o.id) AS total
		FROM
			tb_order AS o
		` + commonSubQuery + `
		` + whereClause + `
		` + roleFilterClause + `
	`

	// 准备查询参数
	// 查询参数：子查询中的操作者邮箱(1次) + 抄送邮箱(1次) + 搜索条件参数 + 角色筛选参数
	finalQueryArgs := []interface{}{param.OperatorEmail, param.OperatorEmail}
	finalQueryArgs = append(finalQueryArgs, queryArgs...)
	finalQueryArgs = append(finalQueryArgs, roleFilterArgs...)

	// 计数参数：子查询中的操作者邮箱(1次) + 抄送邮箱(1次) + 搜索条件参数 + 角色筛选参数
	finalCountArgs := []interface{}{param.OperatorEmail, param.OperatorEmail}
	finalCountArgs = append(finalCountArgs, countArgs...)
	finalCountArgs = append(finalCountArgs, roleFilterArgs...)

	// 执行计数查询
	totalInt, err := dao.Count(ctx, tx, countSQL, finalCountArgs)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyAuditOrdersWithRole count query failed: %v", err)
		return nil, 0, err
	}
	total := int64(totalInt)

	// 执行分页查询 - 直接返回值类型切片
	orders, err := PagedQuery[[]OrderWithRoleInfo](ctx, tx, baseSQL, finalQueryArgs, param.PageNum, param.PageSize)
	if err != nil {
		ctxlog.WithCtx(ctx).Errorf("GetMyAuditOrdersWithRole data query failed: %v", err)
		return nil, 0, err
	}

	ctxlog.WithCtx(ctx).Infof("GetMyAuditOrdersWithRole success, found %d audit orders (total: %d) for operator %s",
		len(orders), total, param.OperatorEmail)
	return orders, total, nil
}

// buildAuditRoleFilterClause 构建审批工单的角色筛选条件
func (dao *OrderDao) buildAuditRoleFilterClause(filterByRole []string) (string, []interface{}) {
	if len(filterByRole) == 0 {
		// 如果没有筛选条件，返回默认条件：有审批权限或被抄送
		return "AND (order_combined.max_audit_priority > 0 OR order_combined.is_cc_to_me = 1)", []interface{}{}
	}

	var conditions []string
	var args []interface{}

	for _, role := range filterByRole {
		switch role {
		case RoleToBeApproved:
			// 待我审批：审批状态为3（待审），只有工单进行中有效
			conditions = append(conditions, "order_combined.max_audit_priority = 3 and o.result = 0")
		case RoleWillBeApproved:
			// 将来审批：审批状态为2（将审），只有工单进行中有效
			conditions = append(conditions, "order_combined.max_audit_priority = 2 and o.result = 0")
		case RoleAlreadyApproved:
			// 我已审批：审批状态为1（已审），只有工单进行中有效
			conditions = append(conditions, "(order_combined.max_audit_priority = 1) and o.result = 0")
		case RoleCcToMe:
			// 抄送我的：抄送状态为1
			conditions = append(conditions, "order_combined.is_cc_to_me = 1")
		case RoleCompleted:
			// 完结：工单状态为不为0（完结），只包含和我有关的完结工单：我审批过 或 被抄送过
			conditions = append(conditions, "(o.result != 0 AND (order_combined.max_audit_priority > 0 OR order_combined.is_cc_to_me = 1))")
		}
	}

	if len(conditions) == 0 {
		// 如果没有有效的筛选条件，返回默认条件
		return "AND (order_combined.max_audit_priority > 0 OR order_combined.is_cc_to_me = 1)", []interface{}{}
	}

	// 使用OR连接多个条件，表示满足任一条件即可
	filterClause := "AND (" + strings.Join(conditions, " OR ") + ")"
	return filterClause, args
}

// buildOrderRoleFilterClause 构建进行中/已完结工单的角色筛选条件
func (dao *OrderDao) buildOrderRoleFilterClause(filterByRole []string, userEmail string) (string, []interface{}) {
	if len(filterByRole) == 0 {
		// 如果没有筛选条件，返回默认条件：我申请的或抄送给我的
		return "AND (o.proposer_email = ? OR occ.cc_email IS NOT NULL)", []interface{}{userEmail}
	}

	var conditions []string
	var args []interface{}

	for _, role := range filterByRole {
		switch role {
		case RoleApplicant:
			// 我申请的：申请人邮箱等于当前用户邮箱
			conditions = append(conditions, "o.proposer_email = ?")
			args = append(args, userEmail)
		case RoleCcToMe:
			// 抄送我的：抄送表中有当前用户的记录
			conditions = append(conditions, "occ.cc_email IS NOT NULL")
		}
	}

	if len(conditions) == 0 {
		// 如果没有有效的筛选条件，返回默认条件
		return "AND (o.proposer_email = ? OR occ.cc_email IS NOT NULL)", []interface{}{userEmail}
	}

	// 使用OR连接多个条件，表示满足任一条件即可
	filterClause := "AND (" + strings.Join(conditions, " OR ") + ")"
	return filterClause, args
}
